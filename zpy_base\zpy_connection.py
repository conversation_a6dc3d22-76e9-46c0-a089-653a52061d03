import clr, os, winreg
from itertools import islice
 
class PythonStandaloneApplication(object):
    """
    与 Zemax OpticStudio 进行交互的独立应用程序类。
    提供初始化连接、文件操作和数据转换等功能。
    """

    class LicenseException(Exception):
        """许可证无效时抛出的异常。"""
        pass
    class ConnectionException(Exception):
        """无法初始化 .NET 连接时抛出的异常。"""
        pass
    class InitializationException(Exception):
        """无法初始化 Zemax 应用程序时抛出的异常。"""
        pass
    class SystemNotPresentException(Exception):
        """无法获取主系统时抛出的异常。"""
        pass
 
    def __init__(self, path=None):
        """
        初始化 Zemax 连接。
        Args:
            path (str, optional): 自定义初始化路径。默认为 None，使用默认路径。
        Raises:
            InitializationException: 如果无法初始化 Zemax 应用程序。
            ConnectionException: 如果无法建立 .NET 连接。
            LicenseException: 如果许可证无效。
            SystemNotPresentException: 如果无法获取主系统。
        """
        # determine location of ZOSAPI_NetHelper.dll & add as reference
        aKey = winreg.OpenKey(winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER), r"Software\Zemax", 0, winreg.KEY_READ)
        zemaxData = winreg.QueryValueEx(aKey, 'ZemaxRoot')
        NetHelper = os.path.join(os.sep, zemaxData[0], r'ZOS-API\Libraries\ZOSAPI_NetHelper.dll')
        winreg.CloseKey(aKey)
        clr.AddReference(NetHelper)
        import ZOSAPI_NetHelper
        
        # Find the installed version of OpticStudio
        if path is None:
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize()
        else:
            # Note -- uncomment the following line to use a custom initialization path
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize(path)
        
        # determine the ZOS root directory
        if isInitialized:
            dir = ZOSAPI_NetHelper.ZOSAPI_Initializer.GetZemaxDirectory()
        else:
            raise PythonStandaloneApplication.InitializationException("Unable to locate Zemax OpticStudio.  Try using a hard-coded path.")
 
        # add ZOS-API referencecs
        clr.AddReference(os.path.join(os.sep, dir, "ZOSAPI.dll"))
        clr.AddReference(os.path.join(os.sep, dir, "ZOSAPI_Interfaces.dll"))
        import ZOSAPI
 
        # create a reference to the API namespace
        self.ZOSAPI = ZOSAPI
 
        # create a reference to the API namespace
        self.ZOSAPI = ZOSAPI
 
        # Create the initial connection class
        self.TheConnection = ZOSAPI.ZOSAPI_Connection()
 
        if self.TheConnection is None:
            raise PythonStandaloneApplication.ConnectionException("Unable to initialize .NET connection to ZOSAPI")
 
        self.TheApplication = self.TheConnection.CreateNewApplication()
        if self.TheApplication is None:
            raise PythonStandaloneApplication.InitializationException("Unable to acquire ZOSAPI application")
 
        if self.TheApplication.IsValidLicenseForAPI == False:
            raise PythonStandaloneApplication.LicenseException("License is not valid for ZOSAPI use")
 
        self.TheSystem = self.TheApplication.PrimarySystem
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
 
    def __del__(self):
        if self.TheApplication is not None:
            self.TheApplication.CloseApplication()
            self.TheApplication = None
        
        self.TheConnection = None
    
    def OpenFile(self, filepath, saveIfNeeded):
        """
        打开指定的 Zemax 文件。
        Args:
            filepath (str): 文件路径。
            saveIfNeeded (bool): 是否在需要时保存文件。
        Raises:
            SystemNotPresentException: 如果无法获取主系统。
        """
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
        self.TheSystem.LoadFile(filepath, saveIfNeeded)
 
    def CloseFile(self, save="True"):
        """
        关闭当前打开的文件。
        Args:
            save (bool): 是否保存文件。
        Raises:
            SystemNotPresentException: 如果无法获取主系统。
        """
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
        self.TheSystem.Close(save)

    def SaveFile(self, filePath):
        """
        保存当前打开的文件到指定路径。
        Args:
            filePath (str): 要保存的文件路径。
        Raises:
            SystemNotPresentException: 如果无法获取主系统。
        """
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
        self.TheSystem.SaveAs(filePath)
 
    def SamplesDir(self):
        """
        获取 Zemax 样本目录。
        Returns:
            str: 样本目录路径。
        Raises:
            InitializationException: 如果无法获取 Zemax 应用程序。
        """
        if self.TheApplication is None:
            raise PythonStandaloneApplication.InitializationException("Unable to acquire ZOSAPI application")
 
        return self.TheApplication.SamplesDir
 
    def ExampleConstants(self):
        """
        获取当前许可证类型。
        Returns:
            str: 许可证类型（Premium/Professional/Standard/Invalid）。
        """
        if self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusType.PremiumEdition:
            return "Premium"
        elif self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusTypeProfessionalEdition:
            return "Professional"
        elif self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusTypeStandardEdition:
            return "Standard"
        else:
            return "Invalid"
    
    def reshape(self, data, x, y, transpose = False):
        """
        将 ZOSAPI 返回的二维数组转换为 Python 列表。
        Args:
            data (System.Double[,]): ZOSAPI 返回的二维数组。
            x (int): 新列表的行数。
            y (int): 新列表的列数。
            transpose (bool, optional): 是否转置数据。默认为 False。
        Returns:
            list: 转换后的二维列表。
        """
        if type(data) is not list:
            data = list(data)
        var_lst = [y] * x;
        it = iter(data)
        res = [list(islice(it, i)) for i in var_lst]
        if transpose:
            return self.transpose(res);
        return res
    
    def transpose(self, data):
        """
        转置二维列表。
        Args:
            data (list): 二维列表。
        Returns:
            list: 转置后的二维列表。
        """
        if type(data) is not list:
            data = list(data)
        return list(map(list, zip(*data)))

if __name__ == '__main__':
    """
    主程序入口，用于测试类功能。
    """
    zos = PythonStandaloneApplication()
    
    ZOSAPI = zos.ZOSAPI
    TheApplication = zos.TheApplication
    TheSystem = zos.TheSystem

    print(ZOSAPI)
    print(TheApplication)
    print(TheSystem)

    # 使用 data 目录下的 19-zemax.ZMX 文件进行测试
    test_file_path = os.path.join('data', '19-zemax.ZMX')
    
    try:
        zos.OpenFile(test_file_path, saveIfNeeded=False)
        print(f"成功打开文件: {test_file_path}")
    except Exception as e:
        print(f"打开文件时发生错误: {e}")
    
    
