# zemax光学分析Python编程核心步骤
# 1. 初始化ZOS API
zos = PythonStandaloneApplication()

# 2. 加载光学系统文件
TheSystem.LoadFile(testFile, False)

# 3. 创建分析
analysis = TheSystem.Analyses.New_Analysis(ZOSAPI.Analysis.AnalysisIDM.StandardSpot)

> 分析类型使用如下枚举类型

```python
enum   AnalysisIDM { 
   XXXTemplateXXX = -1 , RayFan , OpticalPathFan , PupilAberrationFan , 
   FieldCurvatureAndDistortion , FocalShiftDiagram , GridDistortion , LateralColor , 
   LongitudinalAberration , RayTrace , SeidelCoefficients , SeidelDiagram , 
   ZernikeAnnularCoefficients , ZernikeCoefficientsVsField , ZernikeFringeCoefficients , ZernikeStandardCoefficients , 
   FftMtf , FftThroughFocusMtf , GeometricThroughFocusMtf , GeometricMtf , 
   FftMtfMap , GeometricMtfMap , FftSurfaceMtf , FftMtfvsField , 
   GeometricMtfvsField , HuygensMtfvsField , HuygensMtf , HuygensSurfaceMtf , 
   HuygensThroughFocusMtf , FftPsf , FftPsfCrossSection , FftPsfLineEdgeSpread
 ,    HuygensPsfCrossSection , HuygensPsf , DiffractionEncircledEnergy , GeometricEncircledEnergy , 
   GeometricLineEdgeSpread , ExtendedSourceEncircledEnergy , SurfaceCurvatureCross , SurfacePhaseCross , 
   SurfaceSagCross , SurfaceCurvature , SurfacePhase , SurfaceSag , 
   StandardSpot , ThroughFocusSpot , FullFieldSpot , MatrixSpot , 
   ConfigurationMatrixSpot , RMSField , RMSFieldMap , RMSLambdaDiagram , 
   RMSFocus , Foucault , Interferogram , WavefrontMap , 
   DetectorViewer , Draw2D , Draw3D , ImageSimulation , 
   GeometricImageAnalysis , IMABIMFileViewer , GeometricBitmapImageAnalysis , BitmapFileViewer , 
   LightSourceAnalysis , PartiallyCoherentImageAnalysis , ExtendedDiffractionImageAnalysis , BiocularFieldOfViewAnalysis , 
   BiocularDipvergenceConvergence , RelativeIllumination , VignettingDiagramSettings , FootprintSettings , 
   YYbarDiagram , PowerFieldMapSettings , PowerPupilMapSettings , IncidentAnglevsImageHeight , 
   FiberCouplingSettings , YNIContributions , SagTable , CardinalPoints , 
   DispersionDiagram , GlassMap , AthermalGlassMap , InternalTransmissionvsWavelength , 
   DispersionvsWavelength , GrinProfile , GradiumProfile , UniversalPlot1D , 
   UniversalPlot2D , PolarizationRayTrace , PolarizationPupilMap , Transmission , 
   PhaseAberration , TransmissionFan , ParaxialGaussianBeam , SkewGaussianBeam
 ,    PhysicalOpticsPropagation , BeamFileViewer , ReflectionvsAngle , TransmissionvsAngle , 
   AbsorptionvsAngle , DiattenuationvsAngle , PhasevsAngle , RetardancevsAngle
 ,    ReflectionvsWavelength , TransmissionvsWavelength , AbsorptionvsWavelength , DiattenuationvsWavelength , 
   PhasevsWavelength , RetardancevsWavelength , DirectivityPlot , SourcePolarViewer , 
   PhotoluminscenceViewer , SourceSpectrumViewer , RadiantSourceModelViewerSettings , SurfaceDataSettings , 
   PrescriptionDataSettings , FileComparatorSettings , PartViewer , ReverseRadianceAnalysis , 
   PathAnalysis , FluxvsWavelength , RoadwayLighting , SourceIlluminationMap , 
   ScatterFunctionViewer , ScatterPolarPlotSettings , ZemaxElementDrawing , ShadedModel , 
   NSCShadedModel , NSC3DLayout , NSCObjectViewer , RayDatabaseViewer , 
   ISOElementDrawing , SystemData , TestPlateList , SourceColorChart1931 , 
   SourceColorChart1976 , PrescriptionGraphic , CriticalRayTracer , ContrastLoss , 
   CoatingListing , FullFieldAberration , SurfaceSlope , SurfaceSlopeCross , 
   QuickYield , SystemCheck , ToleranceYield , ToleranceHistogram , 
   DiffEfficiency2D , DiffEfficiencyAngular , DiffEfficiencyChromatic , NSCSingleRayTrace = 151 
} 
```

# 4. 执行分析并等待完成
analysis.ApplyAndWaitForCompletion()

# 5. 获取分析结果对象
results = analysis.GetResults()

数据类型参考如下代码进行可视化

```python
# 数据网格（DataGrid）数据网格通常是一个二维数组
plt.imshow(data_grid, cmap='hot', interpolation='nearest')
plt.colorbar()
plt.title('Data Grid Visualization')
plt.show()

# 序列数据（DataSeries）是一组相关的数据点
plt.plot(x_values, y_values, label='Data Series')
plt.xlabel('X-axis Label')
plt.ylabel('Y-axis Label')
plt.title('Data Series Visualization')
plt.legend()
plt.show()

# 散点数据（ScatterPoints）由一组点组成
plt.scatter(x_points, y_points, color='blue', s=1)  # s为点的大小
plt.title('Scatter Points Visualization')
plt.xlabel('X-axis Label')
plt.ylabel('Y-axis Label')
plt.show()

# 光线数据（RayData）包含光线的传播信息，包括光线的起点、方向、波长等
plt.quiver(x_start, y_start, x_direction, y_direction, color='red')
plt.title('Ray Data Visualization')
plt.xlabel('X-axis Label')
plt.ylabel('Y-axis Label')
plt.show()
```


# Zemax光学分析Python编程步骤示例
# 1. 初始化ZOS API
zos = PythonStandaloneApplication()

# 2. 加载光学系统文件
TheSystem.LoadFile(testFile, False)

# 3. 获取系统视场数量
num_fields = TheSystem.SystemData.Fields.NumberOfFields

# 4. 获取系统波长数量
num_wavelengths = TheSystem.SystemData.Wavelengths.NumberOfWavelengths

# 5. 设置批量光线追踪
raytrace = TheSystem.Tools.OpenBatchRayTrace()

# 6. 创建未偏振光线数据对象
normUnPolData = raytrace.CreateNormUnpol(max_rays + 1, ZOSAPI.Tools.RayTrace.RaysType.Real, nsur)

# 7. 添加光线到批量追踪
normUnPolData.AddRay(Int32(wave), Double(hx), Double(hy), Double(px), Double(py), Enum.Parse(ZOSAPI.Tools.RayTrace.OPDMode, "None"))

# 8. 运行光线追踪
raytrace.RunAndWaitForCompletion()

# 9. 读取追踪结果
output = normUnPolData.ReadNextResult(sysInt, sysInt, sysInt, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl)

# 10. 可视化结果
plt.plot(py_ary[:], np.squeeze((y_ary[field, wave,:] - chief_ary[field - 1]) * 1000), '-', ms = 4)

# 11. 执行光线扇分析
ray = TheSystem.Analyses.New_Analysis(ZOSAPI.Analysis.AnalysisIDM.RayFan)

# 12. 清理资源
del zos

# 13. 创建标准光斑图分析
spot = TheSystem.Analyses.New_Analysis(ZOSAPI.Analysis.AnalysisIDM.StandardSpot)

# 14. 获取分析设置对象
spot_setting = spot.GetSettings()

# 15. 配置分析参数
spot_setting.Field.SetFieldNumber(0)  # 0表示使用所有视场
spot_setting.Wavelength.SetWavelengthNumber(0)  # 0表示使用所有波长
spot_setting.ReferTo = ZOSAPI.Analysis.Settings.RMS.ReferTo.Centroid  # 设置参考点为质心

# 16. 执行分析并等待完成
spot.ApplyAndWaitForCompletion()

# 17. 获取分析结果对象
spot_results = spot.GetResults()

# 18. 从结果中提取RMS光斑尺寸
rms_spot = spot_results.SpotData.GetRMSSpotSizeFor(field_num, wave_num)

# 19. 从结果中提取几何光斑尺寸
geo_spot = spot_results.SpotData.GetGeoSpotSizeFor(field_num, wave_num)

# 20. 打印分析结果
print('RMS radius: %6.3f' % rms_spot)
print('GEO radius: %6.3f' % geo_spot)

# 21. 可视化结果数据
plt.plot(np.squeeze(x_ary), np.squeeze(y_ary), '.', ms=1)
plt.title('Spot Diagram Analysis Results')

# 22. 释放分析资源
del spot
