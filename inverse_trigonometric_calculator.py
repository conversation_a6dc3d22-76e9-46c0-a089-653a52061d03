#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反三角函数计算器
================

这是一个用于计算反三角函数的独立程序
支持反正弦、反余弦、反正切等常用反三角函数的计算

作者: AI助手
版本: 1.0
"""

import math
import sys


class InverseTrigCalculator:
    """反三角函数计算器类"""
    
    def __init__(self):
        self.functions = {
            '1': ('反正弦 (arcsin)', self.arcsin),
            '2': ('反余弦 (arccos)', self.arccos),
            '3': ('反正切 (arctan)', self.arctan),
            '4': ('反正切2 (arctan2)', self.arctan2),
            '5': ('反正割 (arcsec)', self.arcsec),
            '6': ('反余割 (arccsc)', self.arccsc),
            '7': ('反余切 (arccot)', self.arccot),
            '8': ('度分秒转换工具', self.dms_converter),
        }
    
    def display_menu(self):
        """显示功能菜单"""
        print("\n" + "="*50)
        print("🔢 反三角函数计算器")
        print("="*50)
        print("请选择要计算的反三角函数或使用转换工具:")
        print()
        for key, (name, _) in self.functions.items():
            print(f"  {key}. {name}")
        print("  0. 退出程序")
        print("-"*50)
    
    def get_input(self, prompt, value_type=float):
        """获取用户输入并验证"""
        while True:
            try:
                value = input(prompt)
                if value.lower() in ['q', 'quit', 'exit']:
                    return None
                return value_type(value)
            except ValueError:
                print("❌ 输入格式错误，请输入有效的数字！")
                print("💡 提示：输入 'q' 可返回主菜单")
    
    def decimal_to_dms(self, decimal_degrees):
        """将十进制度数转换为度分秒格式"""
        # 处理负数
        sign = -1 if decimal_degrees < 0 else 1
        decimal_degrees = abs(decimal_degrees)
        
        # 度
        degrees = int(decimal_degrees)
        
        # 分
        minutes_float = (decimal_degrees - degrees) * 60
        minutes = int(minutes_float)
        
        # 秒
        seconds = (minutes_float - minutes) * 60
        
        return sign * degrees, minutes, seconds
    
    def dms_to_decimal(self, degrees, minutes, seconds):
        """将度分秒转换为十进制度数"""
        decimal = abs(degrees) + minutes/60 + seconds/3600
        return decimal if degrees >= 0 else -decimal
    
    def format_result(self, x, func_name, result_rad):
        """格式化并显示计算结果"""
        result_deg = math.degrees(result_rad)
        deg, min_val, sec = self.decimal_to_dms(result_deg)
        
        print(f"\n✅ 计算结果:")
        print(f"   {func_name}({x}) = {result_rad:.6f} 弧度")
        print(f"   {func_name}({x}) = {result_deg:.6f} 度")
        print(f"   {func_name}({x}) = {deg}°{min_val}'{sec:.3f}\"")
        
        # 如果是负数，特别标注
        if result_deg < 0:
            print(f"   💡 注意：负角度表示与正方向相反的方向")
    
    def arcsin(self):
        """计算反正弦"""
        print("\n📐 计算反正弦 (arcsin)")
        print("定义域: [-1, 1]")
        print("值域: [-π/2, π/2] 或 [-90°, 90°]")
        
        x = self.get_input("请输入x值 (范围 -1 到 1): ")
        if x is None:
            return
        
        if not -1 <= x <= 1:
            print("❌ 错误：输入值必须在 [-1, 1] 范围内！")
            return
        
        result_rad = math.asin(x)
        self.format_result(x, "arcsin", result_rad)
    
    def arccos(self):
        """计算反余弦"""
        print("\n📐 计算反余弦 (arccos)")
        print("定义域: [-1, 1]")
        print("值域: [0, π] 或 [0°, 180°]")
        
        x = self.get_input("请输入x值 (范围 -1 到 1): ")
        if x is None:
            return
        
        if not -1 <= x <= 1:
            print("❌ 错误：输入值必须在 [-1, 1] 范围内！")
            return
        
        result_rad = math.acos(x)
        self.format_result(x, "arccos", result_rad)
    
    def arctan(self):
        """计算反正切"""
        print("\n📐 计算反正切 (arctan)")
        print("定义域: (-∞, +∞)")
        print("值域: (-π/2, π/2) 或 (-90°, 90°)")
        
        x = self.get_input("请输入x值: ")
        if x is None:
            return
        
        result_rad = math.atan(x)
        self.format_result(x, "arctan", result_rad)
    
    def arctan2(self):
        """计算反正切2（两参数版本）"""
        print("\n📐 计算反正切2 (arctan2)")
        print("定义域: y和x不能同时为0")
        print("值域: (-π, π] 或 (-180°, 180°]")
        print("💡 arctan2(y, x) 考虑象限，返回从x轴正方向到点(x,y)的角度")
        
        y = self.get_input("请输入y值: ")
        if y is None:
            return
        
        x = self.get_input("请输入x值: ")
        if x is None:
            return
        
        if x == 0 and y == 0:
            print("❌ 错误：x和y不能同时为0！")
            return
        
        result_rad = math.atan2(y, x)
        result_deg = math.degrees(result_rad)
        deg, min_val, sec = self.decimal_to_dms(result_deg)
        
        print(f"\n✅ 计算结果:")
        print(f"   arctan2({y}, {x}) = {result_rad:.6f} 弧度")
        print(f"   arctan2({y}, {x}) = {result_deg:.6f} 度")
        print(f"   arctan2({y}, {x}) = {deg}°{min_val}'{sec:.3f}\"")
        
        # 如果是负数，特别标注
        if result_deg < 0:
            print(f"   💡 注意：负角度表示与正方向相反的方向")
    
    def arcsec(self):
        """计算反正割"""
        print("\n📐 计算反正割 (arcsec)")
        print("定义域: (-∞, -1] ∪ [1, +∞)")
        print("值域: [0, π/2) ∪ (π/2, π] 或 [0°, 90°) ∪ (90°, 180°]")
        
        x = self.get_input("请输入x值 (|x| ≥ 1): ")
        if x is None:
            return
        
        if -1 < x < 1:
            print("❌ 错误：输入值的绝对值必须大于等于1！")
            return
        
        try:
            result_rad = math.acos(1/x)
            self.format_result(x, "arcsec", result_rad)
        except ZeroDivisionError:
            print("❌ 错误：除零错误！")
    
    def arccsc(self):
        """计算反余割"""
        print("\n📐 计算反余割 (arccsc)")
        print("定义域: (-∞, -1] ∪ [1, +∞)")
        print("值域: [-π/2, 0) ∪ (0, π/2] 或 [-90°, 0°) ∪ (0°, 90°]")
        
        x = self.get_input("请输入x值 (|x| ≥ 1): ")
        if x is None:
            return
        
        if -1 < x < 1:
            print("❌ 错误：输入值的绝对值必须大于等于1！")
            return
        
        try:
            result_rad = math.asin(1/x)
            self.format_result(x, "arccsc", result_rad)
        except ZeroDivisionError:
            print("❌ 错误：除零错误！")
    
    def arccot(self):
        """计算反余切"""
        print("\n📐 计算反余切 (arccot)")
        print("定义域: (-∞, +∞)")
        print("值域: (0, π) 或 (0°, 180°)")
        
        x = self.get_input("请输入x值: ")
        if x is None:
            return
        
        # arccot(x) = π/2 - arctan(x) 或 arccot(x) = arctan(1/x) (x≠0)
        if x == 0:
            result_rad = math.pi / 2
        else:
            result_rad = math.atan(1/x)
            if result_rad < 0:
                result_rad += math.pi
        
        self.format_result(x, "arccot", result_rad)
    
    def dms_converter(self):
        """度分秒转换工具"""
        print("\n🔄 度分秒转换工具")
        print("选择转换方向:")
        print("  1. 十进制度 → 度分秒")
        print("  2. 度分秒 → 十进制度")
        print("  3. 弧度 → 度分秒")
        print("  4. 度分秒 → 弧度")
        
        choice = input("\n请选择转换类型 (1-4): ").strip()
        
        if choice == '1':
            self.decimal_to_dms_converter()
        elif choice == '2':
            self.dms_to_decimal_converter()
        elif choice == '3':
            self.radians_to_dms_converter()
        elif choice == '4':
            self.dms_to_radians_converter()
        else:
            print("❌ 无效选择！")
    
    def decimal_to_dms_converter(self):
        """十进制度转度分秒"""
        print("\n📐 十进制度 → 度分秒")
        
        decimal_deg = self.get_input("请输入十进制度数: ")
        if decimal_deg is None:
            return
        
        deg, min_val, sec = self.decimal_to_dms(decimal_deg)
        
        print(f"\n✅ 转换结果:")
        print(f"   {decimal_deg}° = {deg}°{min_val}'{sec:.3f}\"")
        
        # 显示各部分的含义
        print(f"\n📖 详细说明:")
        print(f"   度 (°): {deg}")
        print(f"   分 ('): {min_val}")
        print(f"   秒 (\"): {sec:.3f}")
    
    def dms_to_decimal_converter(self):
        """度分秒转十进制度"""
        print("\n📐 度分秒 → 十进制度")
        print("💡 提示：分和秒可以输入小数")
        
        degrees = self.get_input("请输入度数: ", int)
        if degrees is None:
            return
        
        minutes = self.get_input("请输入分数 (0-59): ")
        if minutes is None:
            return
        
        if not 0 <= minutes < 60:
            print("❌ 错误：分数必须在 0-59 范围内！")
            return
        
        seconds = self.get_input("请输入秒数 (0-59): ")
        if seconds is None:
            return
        
        if not 0 <= seconds < 60:
            print("❌ 错误：秒数必须在 0-59 范围内！")
            return
        
        decimal_deg = self.dms_to_decimal(degrees, minutes, seconds)
        
        print(f"\n✅ 转换结果:")
        print(f"   {degrees}°{minutes}'{seconds}\" = {decimal_deg:.6f}°")
        
        # 同时显示弧度
        radians = math.radians(decimal_deg)
        print(f"   {degrees}°{minutes}'{seconds}\" = {radians:.6f} 弧度")
    
    def radians_to_dms_converter(self):
        """弧度转度分秒"""
        print("\n📐 弧度 → 度分秒")
        
        radians = self.get_input("请输入弧度值: ")
        if radians is None:
            return
        
        decimal_deg = math.degrees(radians)
        deg, min_val, sec = self.decimal_to_dms(decimal_deg)
        
        print(f"\n✅ 转换结果:")
        print(f"   {radians} 弧度 = {decimal_deg:.6f}°")
        print(f"   {radians} 弧度 = {deg}°{min_val}'{sec:.3f}\"")
    
    def dms_to_radians_converter(self):
        """度分秒转弧度"""
        print("\n📐 度分秒 → 弧度")
        print("💡 提示：分和秒可以输入小数")
        
        degrees = self.get_input("请输入度数: ", int)
        if degrees is None:
            return
        
        minutes = self.get_input("请输入分数 (0-59): ")
        if minutes is None:
            return
        
        if not 0 <= minutes < 60:
            print("❌ 错误：分数必须在 0-59 范围内！")
            return
        
        seconds = self.get_input("请输入秒数 (0-59): ")
        if seconds is None:
            return
        
        if not 0 <= seconds < 60:
            print("❌ 错误：秒数必须在 0-59 范围内！")
            return
        
        decimal_deg = self.dms_to_decimal(degrees, minutes, seconds)
        radians = math.radians(decimal_deg)
        
        print(f"\n✅ 转换结果:")
        print(f"   {degrees}°{minutes}'{seconds}\" = {decimal_deg:.6f}°")
        print(f"   {degrees}°{minutes}'{seconds}\" = {radians:.6f} 弧度")
    
    def run(self):
        """主运行循环"""
        print("🎉 欢迎使用反三角函数计算器！")
        print("💡 提示：计算过程中输入 'q' 可返回主菜单")
        
        while True:
            self.display_menu()
            
            choice = input("\n请选择功能 (0-8): ").strip()
            
            if choice == '0':
                print("\n👋 感谢使用反三角函数计算器！再见！")
                break
            elif choice in self.functions:
                try:
                    _, func = self.functions[choice]
                    func()
                    input("\n📝 按回车键继续...")
                except KeyboardInterrupt:
                    print("\n\n⚠️  程序被中断，返回主菜单...")
                    continue
            else:
                print("❌ 无效选择，请输入 0-8 之间的数字！")
        
        return 0


def main():
    """主函数"""
    try:
        calculator = InverseTrigCalculator()
        return calculator.run()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
        return 0
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 