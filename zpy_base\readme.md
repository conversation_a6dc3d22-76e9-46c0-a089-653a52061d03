# zpy_connection.py

### 类名

``PythonStandaloneApplication``

### 功能描述

`PythonStandaloneApplication` 类用于与 Zemax OpticStudio 进行交互，提供初始化连接、文件操作和数据转换等功能。该类封装了与 Zemax API 的交互，使得用户能够方便地进行光学系统的分析和操作。

### 输入参数

- `path` (str, optional): 自定义初始化路径。默认为 `None`，使用默认路径。

### 输出参数

- 无直接输出参数，但类的实例化会创建与 Zemax 的连接。

### 相关接口

- `__init__(self, path=None)`: 初始化 Zemax 连接，可能抛出以下异常：

  - `InitializationException`: 如果无法初始化 Zemax 应用程序。
  - `ConnectionException`: 如果无法建立 .NET 连接。
  - `LicenseException`: 如果许可证无效。
  - `SystemNotPresentException`: 如果无法获取主系统。
- `OpenFile(self, filepath, saveIfNeeded)`: 打开指定的 Zemax 文件。

  - `filepath` (str): 文件路径。
  - `saveIfNeeded` (bool): 是否在需要时保存文件。
- `CloseFile(self, save="True")`: 关闭当前打开的文件。

  - `save` (bool): 是否保存文件。
- `SaveFile(self, filePath)`: 保存当前打开的文件到指定路径。

  - `filePath` (str): 要保存的文件路径。
- `SamplesDir(self)`: 获取 Zemax 样本目录，返回样本目录路径。
- `ExampleConstants(self)`: 获取当前许可证类型，返回字符串（如 "Premium"、"Professional"、"Standard" 或 "Invalid"）。
- `reshape(self, data, x, y, transpose=False)`: 将 ZOSAPI 返回的二维数组转换为 Python 列表。

  - `data` (System.Double[,]): ZOSAPI 返回的二维数组。
  - `x` (int): 新列表的行数。
  - `y` (int): 新列表的列数。
  - `transpose` (bool, optional): 是否转置数据，默认为 `False`。
- `transpose(self, data)`: 转置二维列表。

  - `data` (list): 二维列表。

### 注意事项

- 确保 Zemax OpticStudio 已安装，并在 Windows 注册表中正确配置。
- 在调用方法之前，确保类已正确初始化。
- 输入参数的类型必须与定义一致，否则可能会引发异常。

# zpy_surfaceinfo.py

### 类名

``ZemaxSurfaceInfo``

### 功能描述

`ZemaxSurfaceInfo` 类用于获取 Zemax 光学系统中表面信息的类。提供获取表面类型、注释、曲率半径、厚度、材料、有效孔径和机械孔径等信息的功能。

### 输入参数

- `zos` (PythonStandaloneApplication): Zemax 连接实例。

### 输出参数

- 无直接输出参数，但类的实例化会创建与 Zemax 的连接。

### 相关接口

- `__init__(self, zos)`: 初始化与 Zemax 的连接。

  - `zos` (PythonStandaloneApplication): Zemax 连接实例。
- `load_file(self, file_path, save_if_needed=False)`: 加载 Zemax 文件。

  - `file_path` (str): Zemax 文件路径。
  - `save_if_needed` (bool, optional): 如果需要，是否保存当前文件。默认为 False。
- `get_surface_count(self)`: 获取系统中的表面数量。

  - 返回 `int`: 表面数量。
- `get_surface_info(self, surface_index)`: 获取指定索引表面的详细信息。

  - `surface_index` (int): 表面索引，从 0 开始。zemax中面的索引也是从0开始。
  - 返回 `Dict[str, Any]`: 包含表面详细信息的字典。
    字典包含的信息如下：

```
# 创建包含表面信息的字典
surface_info = {
    "索引": surface_index,
    "表面类型": surface_type,
    "注释": comment,
    "曲率半径": radius,
    "厚度": thickness,
    "材料": material,
    "有效半口径": semi_dia,
    "机械半口径": mech_semi_dia,
    "是否为光阑": is_stop
}
```

- `get_all_surfaces_info(self)`: 获取所有表面的详细信息。

  - 返回 `List[Dict[str, Any]]`: 包含所有表面信息的列表。
- `print_surface_info(self, surface_index)`: 打印指定表面的详细信息。

  - `surface_index` (int): 表面索引，从 0 开始。
- `print_all_surfaces_info(self)`: 打印所有表面的详细信息。
- `close(self)`: 关闭与 Zemax 的连接并释放资源。

### TODO

- [ ] _get_surface_type_name: 镜面类型不完整，需要校对

### 注意事项

- 确保 Zemax OpticStudio 已正确安装并运行。
- 在调用方法之前，确保类已正确初始化。
- 输入参数的类型必须与定义一致，否则可能会引发异常。

# zpy_sysinfo.py

### 类名

``ZemaxSystemInfo``

### 功能描述

`ZemaxSystemInfo` 类用于获取 Zemax 光学系统信息的类。提供获取孔径、视场、波长等系统信息的功能。

### 输入参数

- `zos` (PythonStandaloneApplication): Zemax 连接实例。

### 输出参数

- 无直接输出参数，但类的实例化会创建与 Zemax 的连接。

### 相关接口

- `__init__(self, zos)`: 初始化与 Zemax 的连接。

  - `zos` (PythonStandaloneApplication): Zemax 连接实例。
- `load_file(self, file_path, save_if_needed=False)`: 加载 Zemax 文件。

  - `file_path` (str): Zemax 文件路径。
  - `save_if_needed` (bool, optional): 如果需要，是否保存当前文件。默认为 False。
- `get_aperture_info(self)`: 获取系统孔径信息。

  - 返回 `Dict[str, Any]`: 包含孔径类型、值等信息的字典。
- `get_field_info(self)`: 获取系统视场信息。

  - 返回 `Dict[str, Any]`: 包含视场类型、点数和各视场点信息的字典。

```
field_info = {
    "类型": field_type,
    "点数": field_count,
    "视场点": field_points
}
```

- `get_wavelength_info(self)`: 获取系统波长信息。

  - 返回 `Dict[str, Any]`: 包含波长数量和各波长值的字典。

```
wavelength_info = {
    "波长数量": wave_count,
    "主波长索引": primary_wave_index,
    "波长列表": wave_values
}
```

- `get_system_info(self)`: 获取完整的系统信息。

  - 返回 `Dict[str, Any]`: 包含孔径、视场和波长信息的完整系统信息字典。
- `print_system_info(self)`: 打印系统信息。
- `close(self)`: 关闭与 Zemax 的连接并释放资源。

### 注意事项

- 确保 Zemax OpticStudio 已正确安装并运行。
- 在调用方法之前，确保类已正确初始化。
- 输入参数的类型必须与定义一致，否则可能会引发异常。

### TODO

- [ ] aperture_types: 孔径类型需要校准

# zpy_transform.py

### 类名

`ZemaxTransform`

### 功能描述

`ZemaxTransform` 类用于为单个镜面添加坐标断点并设置偏心倾斜参数。提供以下核心功能：

- 加载 Zemax 光学系统文件
- 为指定镜面添加前后坐标断点
- 设置精确的偏心(decenter)和倾斜(tilt)参数
- 应用随机变换参数
- 进行波前分析评估系统性能
- 保存变换后的系统文件

### 输入参数

- `zos` (PythonStandaloneApplication): Zemax 连接实例（必需）
- `file_path` (str): Zemax 文件路径（通过 `load_file` 方法）
- `lens_index` (int): 镜面索引（通过 `add_coordinate_breaks` 方法）
- 偏心倾斜参数（通过 `set_tilt_decenter` 方法）:
  - `x_decenter`, `y_decenter`, `z_decenter`: 各方向偏心量(mm)
  - `x_tilt`, `y_tilt`, `z_tilt`: 各方向倾斜角度(度)
  - `order`: 变换顺序(0=先偏心后倾斜,1=先倾斜后偏心)

### 输出参数

- 无直接输出参数，但会修改光学系统并可通过以下方法获取结果:
  - `run_wavefront_analysis()`: 返回波前分析结果(PV/RMS)
  - `analyze_transformed_system()`: 返回系统分析结果
  - `save_transformed_file()`: 返回保存的文件路径

### 相关接口

- `add_coordinate_breaks(lens_index)`: 为指定镜面添加坐标断点
  - 自动在前表面和后表面位置插入 CoordinateBreak 类型表面
  - 自动调整表面间隔保持光学路径长度
- `set_tilt_decenter()`: 设置精确的偏心倾斜参数
  - 后表面参数自动设置为前表面的相反数实现补偿
- `apply_random_transformation()`: 应用随机变换参数
  - 可指定最大偏心量和倾斜角度范围
- `run_wavefront_analysis()`: 运行波前分析
  - 支持多视场多波长分析
- `analyze_transformed_system()`: 综合评估变换后系统
  - 计算平均 PV/RMS 波前误差
- `save_transformed_file()`: 保存变换结果

### 注意事项

1. 操作顺序必须为: 加载文件 → 添加坐标断点 → 设置参数 → 分析/保存
2. 镜面索引从 0 开始，对应 Zemax 中的表面编号
3. 变换后必须调用 `close()` 释放资源
4. 确保 Zemax 已正确安装并配置

# zpy_transform_multi.py

### 类名

`ZemaxTransform` (多镜面版本)

### 功能描述

`ZemaxTransform` 类的多镜面版本，支持批量操作多个镜面的坐标断点和偏心倾斜参数。在单镜面版本基础上增加了：

### 核心增强功能

1. **多镜面数据结构**:

   - 使用 `lens_list` 管理多个镜面参数
   - 每个镜面独立存储原始索引、变换后索引和参数
2. **动态索引管理**:

   - 自动跟踪处理过程中的索引偏移
   - 确保批量操作的原子性和正确性
3. **批量参数应用**:

   - 单次调用完成所有镜面的参数设置
   - 各镜面参数完全独立且自动补偿

### 输入参数

- `lens_list` (List[Dict]): 镜面信息列表，每个元素包含:
  - `lens_index`: 原始镜面索引
  - `params`: 参数字典(同单镜面版本)

### 输出参数

同单镜面版本，但针对所有处理镜面的综合结果

![](../figs/multi_transformation.png)

### 相关接口

- `add_coordinate_breaks(lens_list)`: 批量添加坐标断点
  - 自动按镜面索引排序处理
  - 动态计算实际表面索引
- `set_tilt_decenter()`: 批量应用参数
  - 自动为每个镜面设置前后补偿参数
- 其他接口功能与单镜面版本一致

### 注意事项

1. 必须使用 `sort_lens_list()` 预处理镜面列表
2. 多镜面版本的索引计算更复杂，需仔细检查
3. 批量操作时错误处理更为重要
4. 性能优化明显减少 API 调用次数
