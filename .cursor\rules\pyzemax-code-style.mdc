---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: *.py
alwaysApply: false
---
# Python 编程规范助手

## 约束

在进行与zemax相关的代码开发时，用到的函数必须从  [zos_api1.json](mdc:ZOS-API/zos_api1.json) [zos_api2.json](mdc:ZOS-API/zos_api2.json) [zos_api3.json](mdc:ZOS-API/zos_api3.json) [zos_api4.json](mdc:ZOS-API/zos_api4.json) [zos_api5.json](mdc:ZOS-API/zos_api5.json) 中进行精确的查找和调用

## 1. 类命名
- 类名应使用驼峰命名法（CamelCase），例如 `MyClass`。
- 类名应具有描述性，能够清晰表达类的功能。

## 2. 函数命名
- 函数名应使用小写字母和下划线分隔（snake_case），例如 `my_function`。
- 函数名应简洁明了，能够清楚表达其功能。

## 3. 变量命名
- 变量名应使用小写字母和下划线分隔（snake_case），例如 `my_variable`。
- 变量名应具有描述性，避免使用单字母变量名，除非在循环中。

## 4. 错误机制
- 使用 `try` 和 `except` 块来处理异常，确保程序的健壮性。
- 捕获特定的异常类型，而不是使用通用的 `except`。
- 在捕获异常时，记录错误信息以便于调试。

```python
try:
    # 可能引发异常的代码
except SpecificException as e:
    print(f"发生错误: {e}")
```

## 5. 包管理
- 使用 `conda` 进行包管理，确保环境的可重复性。
- 在项目根目录下创建 `environment.yml` 文件，记录所需的包及其版本。

```yaml
name: my_project
channels:
  - defaults
dependencies:
  - python=3.6
  - pythonnet=2.5.2
  - other-package
```

## 6. 文档和注释
- 使用 docstring 为模块、类和函数提供文档说明。
- 注释应简洁明了，解释复杂的逻辑或重要的实现细节。
- 使用Sphinx生成模块API文档，结合Google风格Docstring规范
```python
def encrypt_data(data: str, key: str) -> bytes:
    """使用AES加密数据
    Args:
        data: 待加密的明文
        key: 16/24/32字节的密钥
    Returns:
        加密后的字节流
    """
```

## 7. 代码格式
- 使用 4 个空格进行缩进，避免使用制表符（tab）。
- 每行代码不应超过 79 个字符。

## 8. 版本控制
- 适当的提示用户自己进行版本控制。

## 9. 测试
- 每一个python文件是一个类，这个类可以作为包被别的库进行调用。
- 为每一个类提供一个**main**函数进行示例验证。

## 10. 代码审查
- 定期进行代码审查，确保代码质量和一致性。
- 代码审查应包括功能、可读性和性能等方面的检查。

## 11. 模块化开发
- 每个模块/包专注于一个功能领域（如数据处理、日志工具、API接口），避免混合不相关功能
- 在 __init__.py 中定义 __all__ 变量，控制 from package import * 时暴露的内容
```
# core/__init__.py
__all__ = ['utils', 'models']
from .utils import data_clean, format_output
```

- 在__init__.py中声明模块版本号
```
__version__ = "1.2.0"
```

## 12. 导入方式
- 精确导入​​：优先使用 from module import func 而非 import *，减少命名冲突
- 别名优化​​：对长模块名使用别名（如 import pandas as pd）提升代码简洁性
- 相对导入​​：在包内部使用 from .submodule import Class 增强内聚性

## 13. matplotlib绘图中文乱码解决方案

```python
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

# 示例绘图
plt.plot([1, 2, 3], [4, 5, 6])
plt.title("中文标题")  # 测试中文显示
plt.show()
```







