<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>801</width>
    <height>776</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QGroupBox" name="resultGroupBox">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>590</y>
      <width>781</width>
      <height>137</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string>QGroupBox { font-weight: bold; }</string>
    </property>
    <property name="title">
     <string>计算结果</string>
    </property>
    <layout class="QGridLayout" name="resultLayout">
     <item row="2" column="2">
      <widget class="QLabel" name="labelUnitC">
       <property name="text">
        <string>mm</string>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="labelResultL">
       <property name="styleSheet">
        <string>QLabel { font-size: 12px; }</string>
       </property>
       <property name="text">
        <string>主次镜间隔 L:</string>
       </property>
      </widget>
     </item>
     <item row="0" column="2">
      <widget class="QLabel" name="labelUnitL">
       <property name="text">
        <string>mm</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEditResultL">
       <property name="styleSheet">
        <string>QLineEdit { 
            background-color: #ecf0f1; 
            font-size: 14px; 
            font-weight: bold; 
            color: #2c3e50; 
            padding: 5px; 
            border: 2px solid #bdc3c7; 
            border-radius: 3px; 
          }</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="labelFormulaL">
       <property name="styleSheet">
        <string>QLabel { font-size: 10px; color: #7f8c8d; font-style: italic; }</string>
       </property>
       <property name="text">
        <string>计算公式: L = L1 + L7 - L2 - L3 - (L4 - L5) - L6</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="labelResultC">
       <property name="styleSheet">
        <string>QLabel { font-size: 12px; }</string>
       </property>
       <property name="text">
        <string>垫片厚度预测值 c:</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="lineEditResultC">
       <property name="styleSheet">
        <string>QLineEdit { 
            background-color: #e8f5e8; 
            font-size: 14px; 
            font-weight: bold; 
            color: #27ae60; 
            padding: 5px; 
            border: 2px solid #2ecc71; 
            border-radius: 3px; 
          }</string>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QGroupBox" name="inputGroupBox">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>781</width>
      <height>271</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string>QGroupBox { font-weight: bold; }</string>
    </property>
    <property name="title">
     <string>输入参数 (单位: mm)</string>
    </property>
    <layout class="QGridLayout" name="gridLayout_3">
     <item row="0" column="2">
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="6" column="1">
        <widget class="QDoubleSpinBox" name="spinBoxL6">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>0.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QDoubleSpinBox" name="spinBoxL1">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>0.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="labelL4">
         <property name="text">
          <string>L4 - 主镜边缘与主镜安装面上端面的距离:</string>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="labelL5">
         <property name="text">
          <string>L5 - 主镜边缘距主镜顶点的高度:</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QDoubleSpinBox" name="spinBoxL5">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>0.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="labelL2">
         <property name="text">
          <string>L2 - 次镜标准垫片厚度:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="labelL3">
         <property name="text">
          <string>L3 - 次镜高度:</string>
         </property>
        </widget>
       </item>
       <item row="8" column="1">
        <widget class="QPushButton" name="calculateButton">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string>QPushButton { 
         background-color: #3498db; 
         color: white; 
         font-size: 14px; 
         font-weight: bold; 
         padding: 10px; 
         border-radius: 5px; 
         border: none; 
       }
       QPushButton:hover { 
         background-color: #2980b9; 
       }
       QPushButton:pressed { 
         background-color: #21618c; 
       }</string>
         </property>
         <property name="text">
          <string>计算主次镜间隔</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QDoubleSpinBox" name="spinBoxL3">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>0.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="labelL6">
         <property name="text">
          <string>L6 - 主镜上端面与柔节安装面的距离:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QDoubleSpinBox" name="spinBoxL4">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>0.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QDoubleSpinBox" name="spinBoxL2">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>0.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="labelL1">
         <property name="text">
          <string>L1 - 主次镜支架高度:</string>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QLabel" name="labelL7">
         <property name="text">
          <string>L7 - 主次镜支架移动量:</string>
         </property>
        </widget>
       </item>
       <item row="7" column="1">
        <widget class="QDoubleSpinBox" name="spinBoxL7">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>0.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item row="0" column="0">
      <widget class="QWidget" name="widget_picture" native="true">
       <property name="minimumSize">
        <size>
         <width>300</width>
         <height>220</height>
        </size>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </widget>
   <widget class="QGroupBox" name="inputGroupBox_2">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>280</y>
      <width>781</width>
      <height>299</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string>QGroupBox { font-weight: bold; }</string>
    </property>
    <property name="title">
     <string>装调实际参数</string>
    </property>
    <layout class="QGridLayout" name="gridLayout_4">
     <item row="0" column="0">
      <layout class="QGridLayout" name="gridLayout">
       <item row="5" column="3">
        <widget class="QDoubleSpinBox" name="Tilt3_Y">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="label_Tilt1_Y">
         <property name="text">
          <string>主镜倾斜Y：</string>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_Dencenter3_X">
         <property name="text">
          <string>三镜偏心X：</string>
         </property>
        </widget>
       </item>
       <item row="2" column="4" colspan="2">
        <widget class="QLabel" name="labelLens2_surface">
         <property name="text">
          <string>次镜实际面形：</string>
         </property>
        </widget>
       </item>
       <item row="7" column="1">
        <widget class="QDoubleSpinBox" name="Decenter4_Y">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_Dencenter2_X">
         <property name="text">
          <string>次镜偏心X：</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QDoubleSpinBox" name="Decenter2_Y">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="4" column="3">
        <widget class="QDoubleSpinBox" name="Tilt3_X">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QDoubleSpinBox" name="Tilt1_Y">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="5" column="4" colspan="3">
        <widget class="QLineEdit" name="lineEdit_Lens3"/>
       </item>
       <item row="0" column="6">
        <widget class="QPushButton" name="choose1">
         <property name="text">
          <string>选择文件</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QDoubleSpinBox" name="Decenter1">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QDoubleSpinBox" name="Decenter3_Y">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="4" column="6">
        <widget class="QPushButton" name="choose3">
         <property name="text">
          <string>选择文件</string>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="label_Tilt2_X">
         <property name="text">
          <string>次镜倾斜X：</string>
         </property>
        </widget>
       </item>
       <item row="5" column="2">
        <widget class="QLabel" name="label_Tilt3_Y">
         <property name="text">
          <string>三镜倾斜Y：</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QDoubleSpinBox" name="Decenter1_2">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="4" column="4" colspan="2">
        <widget class="QLabel" name="labelLens3_surface">
         <property name="text">
          <string>三镜实际面形：</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="label_Tilt1_X">
         <property name="text">
          <string>主镜倾斜X：</string>
         </property>
        </widget>
       </item>
       <item row="3" column="4" colspan="3">
        <widget class="QLineEdit" name="lineEdit_Lens2"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_Dencenter1_Y">
         <property name="text">
          <string>主镜偏心Y：</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4" colspan="2">
        <widget class="QLabel" name="labelLens1_surface">
         <property name="text">
          <string>主镜实际面形：</string>
         </property>
        </widget>
       </item>
       <item row="6" column="6">
        <widget class="QPushButton" name="choose4">
         <property name="text">
          <string>选择文件</string>
         </property>
        </widget>
       </item>
       <item row="6" column="4" colspan="2">
        <widget class="QLabel" name="labelLens4_surface">
         <property name="text">
          <string>四镜实际面形：</string>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="label_Tilt2_Y">
         <property name="text">
          <string>次镜倾斜Y：</string>
         </property>
        </widget>
       </item>
       <item row="7" column="4" colspan="3">
        <widget class="QLineEdit" name="lineEdit_Lens4"/>
       </item>
       <item row="2" column="1">
        <widget class="QDoubleSpinBox" name="Decenter2_X">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QLabel" name="label_Dencenter4_Y">
         <property name="text">
          <string>四镜偏心Y：</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_Dencenter2_Y">
         <property name="text">
          <string>次镜偏心Y：</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="label_Dencenter1_X">
         <property name="text">
          <string>主镜偏心X：</string>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QDoubleSpinBox" name="Tilt1_X">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_Dencenter3_Y">
         <property name="text">
          <string>三镜偏心Y：</string>
         </property>
        </widget>
       </item>
       <item row="1" column="4" colspan="3">
        <widget class="QLineEdit" name="lineEdit_Lens1"/>
       </item>
       <item row="6" column="3">
        <widget class="QDoubleSpinBox" name="Tilt4_X">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="7" column="2">
        <widget class="QLabel" name="label_Tilt4_Y">
         <property name="text">
          <string>四镜倾斜Y：</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QDoubleSpinBox" name="Decenter3_X">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="QDoubleSpinBox" name="Decenter4_X">
         <property name="suffix">
          <string> mm</string>
         </property>
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-9999.000000000000000</double>
         </property>
         <property name="maximum">
          <double>9999.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="2" column="6">
        <widget class="QPushButton" name="choose2">
         <property name="text">
          <string>选择文件</string>
         </property>
        </widget>
       </item>
       <item row="6" column="2">
        <widget class="QLabel" name="label_Tilt4_X">
         <property name="text">
          <string>四镜倾斜X：</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QDoubleSpinBox" name="Tilt2_X">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QDoubleSpinBox" name="Tilt2_Y">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="QLabel" name="label_Tilt3_X">
         <property name="text">
          <string>三镜倾斜X：</string>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_Dencenter4_X">
         <property name="text">
          <string>四镜偏心X：</string>
         </property>
        </widget>
       </item>
       <item row="7" column="3">
        <widget class="QDoubleSpinBox" name="Tilt4_Y">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-180.000000000000000</double>
         </property>
         <property name="maximum">
          <double>180.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="8" column="5">
        <widget class="QPushButton" name="comprehensiveAnalysisButton">
         <property name="styleSheet">
          <string>QPushButton { 
         background-color: #27ae60; 
         color: white; 
         font-size: 14px; 
         font-weight: bold; 
         padding: 10px; 
         border-radius: 5px; 
         border: none; 
       }
       QPushButton:hover { 
         background-color: #229954; 
       }
       QPushButton:pressed { 
         background-color: #1e8449; 
       }</string>
         </property>
         <property name="text">
          <string>综合分析</string>
         </property>
        </widget>
       </item>
       <item row="8" column="6">
        <widget class="QPushButton" name="calculateButton_2">
         <property name="styleSheet">
          <string>QPushButton { 
         background-color: #3498db; 
         color: white; 
         font-size: 14px; 
         font-weight: bold; 
         padding: 10px; 
         border-radius: 5px; 
         border: none; 
       }
       QPushButton:hover { 
         background-color: #2980b9; 
       }
       QPushButton:pressed { 
         background-color: #21618c; 
       }</string>
         </property>
         <property name="text">
          <string>次镜垫片厚度预测</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>801</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
