# 次镜垫片厚度计算器

## 项目简介

这是一个专业的光学系统次镜垫片厚度计算工具，用于计算主次镜间隔和所需垫片厚度。该工具特别适用于需要与Zemax光学设计软件配合使用的光学系统设计工作。

## 功能特点

- **图形界面**：直观的PyQt5图形用户界面
- **图片显示**：自动加载光学系统示意图
- **专业计算**：基于光学系统设计原理的精确计算
- **参数验证**：自动验证输入参数的合理性
- **结果检查**：智能检查计算结果并提供警告

## 计算公式

### 主次镜间隔计算
```
L = L1 + L6 + L7 - 4mm - L5 - L2 - L3 + L4
```

### 垫片厚度计算
```
c = L1 + L6 - L - L5 - L2 - L3 + L4
```

## 参数说明

| 参数 | 说明 | 单位 |
|------|------|------|
| L1 | 主次镜支架高度 | mm |
| L2 | 主镜安装端面到主次镜支架安装端面高度 | mm |
| L3 | 主镜边缘与主四镜安装面高度 | mm |
| L4 | 主镜边缘与主镜镜面顶点高度 | mm |
| L5 | 次镜高度 | mm |
| L6 | 次镜安装板厚度 | mm |
| L7 | 主次镜支架移动量 | mm |

## 安装和使用

### 1. 环境要求
- Python 3.7 或更高版本
- Windows 10/11 (推荐)

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 准备图片文件
将您的光学系统示意图保存为 `picture.png` 文件，放在程序根目录下。

### 4. 运行程序
#### 方法1：双击运行
双击 `run_calculator.bat` 文件

#### 方法2：命令行运行
```bash
python SecondLensSpacerCalculator.py
```

## 使用方法

### 基本操作流程

1. **启动程序**
   - 程序启动后会自动加载光学系统示意图
   - 界面左侧显示参数标注图片

2. **输入参数**
   - 在界面右侧的输入区域填入7个光学参数（L1-L7）
   - 所有参数单位为毫米(mm)
   - L7可以为负值，其他参数应为正值

3. **执行计算**
   - 点击"计算主次镜间隔"按钮
   - 系统会自动验证输入参数的合理性
   - 计算结果会显示在结果区域

4. **查看结果**
   - 主次镜间隔 L：显示在 lineEditResultL 中
   - 垫片厚度预测值 c：显示在 lineEditResultC 中

## 界面说明

### 主要区域
- **图片显示区域**：显示光学系统示意图，标注各参数位置
- **参数输入区域**：输入L1-L7参数的数值输入框
- **计算按钮**：执行计算的按钮
- **结果显示区域**：显示计算结果和公式
- **装调实际参数区域**：用于输入实际装调参数

### 控件说明
- `widget_picture`：图片显示控件
- `spinBoxL1-L7`：参数输入框
- `calculateButton`：计算按钮
- `lineEditResultL`：主次镜间隔结果显示
- `lineEditResultC`：垫片厚度结果显示

## 注意事项

### 重要提醒
- ⚠️ **图片文件**：确保 `picture.png` 文件存在且格式正确
- ⚠️ **参数验证**：确保所有输入参数都是合理的物理尺寸
- ⚠️ **单位统一**：所有参数必须使用毫米(mm)作为单位
- ⚠️ **UI文件**：确保 `Ui/SecondLensSpacer.ui` 文件存在

### 常见问题

**Q: 程序启动后看不到图片？**
A: 请检查 `picture.png` 文件是否存在于程序根目录下。

**Q: 点击计算按钮没有反应？**
A: 请检查控制台输出，可能是UI控件连接问题。

**Q: 计算结果为负值？**
A: 请检查输入参数是否合理，特别是各个高度参数的相对关系。

## 文件结构

```
SecondLensSpacerCalculate/
├── Ui/
│   └── SecondLensSpacer.ui          # UI界面文件
├── SecondLensSpacerCalculator.py    # 主程序文件
├── picture.png                      # 光学系统示意图
├── requirements.txt                 # Python依赖文件
├── run_calculator.bat              # 启动脚本
└── README.md                       # 说明文档
```

## 技术支持

如果在使用过程中遇到问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否完整安装
3. UI文件和图片文件是否存在
4. 输入参数是否符合物理实际

## 版本历史

- v1.0: 初始版本，包含基本计算功能和图形界面

---

*光学系统次镜垫片厚度计算器 - 让光学设计更精确* 