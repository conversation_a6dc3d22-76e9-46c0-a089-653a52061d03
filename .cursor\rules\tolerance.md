## 公差分析核心逻辑：基于配置向导的方法

### 仿真

光学系统公差仿真是公差分析的基础，它模拟制造和装配过程中可能引入的误差，评估这些误差对系统性能的影响。基于Zemax API的公差仿真流程包括以下关键步骤：

#### 1. 初始化Zemax连接

首先，建立与Zemax软件的连接，获取必要的API对象。

```python
# 创建Zemax连接
zos = PythonStandaloneApplication()
tolerance_analyzer = ZemaxToleranceAnalysis(zos)
```

#### 2. 加载光学设计文件

加载需要进行公差分析的Zemax设计文件。

```python
# 加载光学设计文件
file_path = os.path.abspath(os.path.join("data", "example.zmx"))
tolerance_analyzer.load_file(file_path)
```

#### 3. 配置公差向导

通过公差向导设置基本的公差参数，包括元件公差和表面公差（仅限于zernike fringe系数）。

```python
# 配置公差向导
tolerance_analyzer.configure_tolerance_wizard(
    element_decenter_x=0.01,         # 元件X方向偏心公差(mm)
    element_decenter_y=0.01,         # 元件Y方向偏心公差(mm)
    element_tilt_x_degrees=0.02,     # 元件X方向倾斜公差(度)
    element_tilt_y_degrees=0.02,     # 元件Y方向倾斜公差(度)
    start_at=1,                      # 公差分析起始面
    stop_at=None,                    # 公差分析结束面(None表示最后一个表面)
    use_surface_irregularity=False,  # 是否使用表面不规则性
    use_index=False,                 # 是否使用折射率公差
    use_index_abbe=False             # 是否使用折射率阿贝数公差
)
```

#### 4. 配置公差分析

设置公差分析的模式、标准和其他参数。

```python
# 配置公差分析
tolerance_analyzer.configure_tolerance_analysis(
    setup_mode="Sensitivity",        # 设置模式(灵敏度分析)
    criterion="RMSSpotRadius",       # 标准(RMS斑点半径)
    criterion_sampling=3,            # 标准采样
    criterion_comp="OptimizeAll_DLS", # 标准比较方式(使用DLS优化所有补偿器)
    criterion_cycle=2,               # 标准循环
    criterion_field="UserDefined",   # 标准字段
    number_of_runs=20,               # 蒙特卡洛运行次数
    number_to_save=5                 # 保存的文件数量
)
```

#### 5. 运行分析

执行公差分析并等待完成。

```python
# 运行公差分析
tolerance_analyzer.run_analysis()
```

#### 6. 保存分析结果

将分析结果保存到文件，以便后续查看和分析。

```python
# 保存分析结果
output_file = os.path.abspath(os.path.join("output", "tolerance_results.tol"))
tolerance_analyzer.save_results(output_file)
```

#### 7. 查看和分析结果

通过公差数据查看器打开结果文件，获取分析数据。

```python
# 打开公差数据查看器
tolerance_viewer = tolerance_analyzer.open_tolerance_data_viewer()

# 获取所有公差操作数
operands = tolerance_analyzer.get_tolerance_operands()
```

通过以上步骤，可以完成光学系统的公差仿真，为后续的蒙特卡洛分析和敏感性分析提供数据基础。

### 蒙特卡洛分析

蒙特卡洛分析是一种基于随机采样的统计方法，用于评估光学系统对公差的敏感度和公差累积效应。它通过多次随机扰动系统参数，模拟制造和装配过程中的随机误差，从而分析系统性能的统计分布。

#### 1. 配置蒙特卡洛分析参数

在公差分析配置中设置蒙特卡洛运行次数和保存文件数量。

```python
# 配置蒙特卡洛分析参数
tolerance_analyzer.configure_tolerance_analysis(
    # ... 其他参数 ...
    number_of_runs=100,              # 蒙特卡洛运行次数
    number_to_save=10                # 保存的文件数量
)
```

#### 2. 获取蒙特卡洛数据

运行分析后，通过公差数据查看器获取蒙特卡洛数据。

```python
# 打开公差数据查看器
tolerance_viewer = tolerance_analyzer.open_tolerance_data_viewer()

# 获取蒙特卡洛数据
monte_carlo_data = tolerance_viewer.MonteCarloData
if monte_carlo_data is None:
    raise ToleranceDataError("无法获取蒙特卡洛数据")
```

#### 3. 提取操作数数据

获取各个公差操作数的数据和统计信息。

```python
# 获取所有公差操作数
operands = tolerance_analyzer.get_tolerance_operands()
print(f"找到{len(operands)}个公差操作数")

# 遍历操作数
for operand in operands:
    operand_index = operand['index']
    operand_type = operand['type']
    stats = operand['statistics']
    print(f"操作数索引: {operand_index}, 类型: {operand_type}")
    print(f"样本大小: {stats['sample_size']}")
    print(f"最小值: {stats['minimum']:.6e}")
    print(f"最大值: {stats['maximum']:.6e}")
    print(f"平均值: {stats['mean']:.6e}")
```

#### 4. 分析操作数统计信息

对特定操作数进行深入统计分析，包括样本分布、标准差、方差等。

```python
# 分析特定操作数的统计信息
def analyze_specific_operand(operand_identifier, tolerance_viewer=None):
    stats = tolerance_analyzer.analyze_operand_statistics(operand_identifier, tolerance_viewer)
  
    # 打印基本统计信息
    print(f"操作数类型: {stats['type']}")
    print(f"样本大小: {stats['sample_size']}")
    print(f"最小值: {stats['minimum']:.6e}")
    print(f"最大值: {stats['maximum']:.6e}")
    print(f"平均值: {stats['mean']:.6e}")
    print(f"样本标准差: {stats['sample_standard_deviation']:.6e}")
    print(f"总体标准差: {stats['population_standard_deviation']:.6e}")
    print(f"方差: {stats['variance']:.6e}")
  
    return stats

# 调用分析函数
stats = analyze_specific_operand(0)  # 分析索引为0的操作数
```

#### 5. 处理直方图数据

分析操作数值的分布情况，获取直方图数据。

```python
# 获取操作数的直方图数据
def get_histogram_data(operand_identifier, tolerance_viewer=None):
    histogram = tolerance_analyzer.get_operand_histogram(operand_identifier, tolerance_viewer)
  
    print(f"直方图区间数量: {len(histogram['bins'])}")
    print(f"下溢: {histogram['underflow']}")
    print(f"上溢: {histogram['overflow']}")
  
    # 打印区间和计数
    for bin_value, count in zip(histogram['bins'], histogram['counts']):
        print(f"区间: {bin_value:.6f}, 计数: {count}")
  
    return histogram

# 调用获取直方图数据函数
histogram = get_histogram_data(0)  # 获取索引为0的操作数的直方图
```

#### 6. 可视化分析结果

使用matplotlib等工具将统计结果可视化，便于直观分析。

```python
# 可视化操作数分布（示例代码，非库中实现）
import matplotlib.pyplot as plt
import numpy as np

def visualize_histogram(histogram):
    bins = histogram['bins']
    counts = histogram['counts']
  
    plt.figure(figsize=(10, 6))
    plt.bar(bins, counts, width=(max(bins) - min(bins)) / len(bins) * 0.8)
    plt.title('操作数值分布')
    plt.xlabel('操作数值')
    plt.ylabel('频次')
    plt.grid(True, alpha=0.3)
    plt.savefig('histogram.png')
    plt.show()

# 调用可视化函数
visualize_histogram(histogram)
```

通过蒙特卡洛分析，设计师可以了解光学系统对各种公差的敏感度，评估系统性能的统计分布，为后续的公差分配和优化提供科学依据。

### 敏感性分析

敏感性分析是公差分析中的关键环节，它评估光学系统对各种公差参数的敏感程度，帮助设计师识别关键公差并合理分配公差预算。本节基于ZemaxToleranceAnalysis类的实现，详细说明敏感性分析的流程和最佳实践。

#### 1. 配置敏感性分析模式

在公差分析配置中设置分析模式为"Sensitivity"。

```python
# 配置敏感性分析
tolerance_analyzer.configure_tolerance_analysis(
    setup_mode="Sensitivity",  # 设置模式为敏感性分析
    # ... 其他参数 ...
)
```

#### 2. 获取敏感性数据

运行分析后，通过公差数据查看器获取敏感性数据。

```python
# 打开公差数据查看器
tolerance_viewer = tolerance_analyzer.open_tolerance_data_viewer()

# 获取敏感性数据
sensitivity_data = tolerance_viewer.SensitivityData
if sensitivity_data is None:
    raise ToleranceDataError("无法获取公差敏感性数据")
```

#### 3. 提取标准(criteria)信息

分析各个评价标准的信息和标称值。

```python
# 获取标准信息
criteria_data = []
for i in range(sensitivity_data.NumberOfCriteria):
    criterion = sensitivity_data.GetCriterion(i)
    criterion_info = {
        "name": criterion.Name,
        "nominal_value": criterion.NominalValue,
        "operand_effects": []
    }
  
    # 获取每个标准对应的操作数效果
    for j in range(criterion.NumberOfOperands):
        effect = criterion.GetEffectByOperand(j)
        criterion_info["operand_effects"].append({
            "operand_index": j,
            "estimated_change_min": effect.EstimatedChangeMinimum,
            "estimated_change_max": effect.EstimatedChangeMaximum,
        })
  
    criteria_data.append(criterion_info)
```

#### 4. 分析补偿器(compensators)数据

提取并分析补偿器的信息和统计参数。

```python
# 获取补偿器数据
compensator_data = []
for i in range(sensitivity_data.NumberOfCompensators):
    compensator = sensitivity_data.GetCompensator(i)
    compensator_data.append({
        "name": compensator.Comment if hasattr(compensator, "Comment") else f"Compensator {i}",
        "minimum": compensator.Minimum,
        "maximum": compensator.Maximum,
        "mean": compensator.Mean if hasattr(compensator, "Mean") else None,
        "std_dev": compensator.PopulationStandardDeviation if hasattr(compensator, "PopulationStandardDeviation") else None,
        "sample_std_dev": compensator.SampleStandardDeviation if hasattr(compensator, "SampleStandardDeviation") else None
    })
```

#### 5. 处理操作数(operands)敏感性数据

分析每个操作数对系统性能的影响。

```python
# 获取操作数数据
operand_data = []
for i in range(sensitivity_data.NumberOfResultOperands):
    operand = sensitivity_data.GetOperand(i)
    operand_info = {
        "name": operand.Comment if hasattr(operand, "Comment") else f"Operand {i}",
        "minimum": operand.Minimum if hasattr(operand, "Minimum") else None,
        "maximum": operand.Maximum if hasattr(operand, "Maximum") else None,
        "criterion_effects": []
    }
  
    # 获取每个操作数对标准的影响
    for j in range(operand.NumberOfCriteria):
        effect = operand.GetEffectOnCriterion(j)
        operand_info["criterion_effects"].append({
            "criterion_index": j,
            "estimated_change_min": effect.EstimatedChangeMinimum,
            "estimated_change_max": effect.EstimatedChangeMaximum,
        })
  
    operand_data.append(operand_info)
```

#### 6. 整合敏感性分析结果

将上述数据整合为完整的敏感性分析结果。

```python
# 整合敏感性分析结果
sensitivity_results = {
    "criteria": criteria_data,
    "compensators": compensator_data,
    "operands": operand_data
}
```

#### 7. 可视化和呈现敏感性分析结果

以结构化方式输出敏感性分析结果，便于解读和决策。

```python
# 打印敏感性分析结果
def print_sensitivity_analysis(sensitivity):
    print("\n" + "=" * 30 + " 敏感性分析结果 " + "=" * 30)
  
    # 打印标准信息
    print("\n--- 标准信息 ---")
    for criterion in sensitivity.get("criteria", []):
        print(f"标准名称: {criterion['name']}")
        print(f"标称值: {criterion['nominal_value']}")
        print("操作数影响:")
        for effect in criterion.get("operand_effects", []):
            print(f"  操作数索引: {effect['operand_index']}, "
                  f"估计变化最小值: {effect['estimated_change_min']}, "
                  f"估计变化最大值: {effect['estimated_change_max']}")
        print("-" * 50)

    # 打印补偿器信息
    print("\n--- 补偿器信息 ---")
    for compensator in sensitivity.get("compensators", []):
        print(f"补偿器名称: {compensator['name']}")
        print(f"最小值: {compensator['minimum']}, 最大值: {compensator['maximum']}")
        print(f"平均值: {compensator['mean']}, "
              f"总体标准差: {compensator['std_dev']}, "
              f"样本标准差: {compensator['sample_std_dev']}")
        print("-" * 50)

    # 打印操作数信息
    print("\n--- 操作数信息 ---")
    for operand in sensitivity.get("operands", []):
        print(f"操作数名称: {operand['name']}")
        print(f"最小值: {operand['minimum']}, 最大值: {operand['maximum']}")
        print("标准影响:")
        for effect in operand.get("criterion_effects", []):
            print(f"  标准索引: {effect['criterion_index']}, "
                  f"估计变化最小值: {effect['estimated_change_min']}, "
                  f"估计变化最大值: {effect['estimated_change_max']}")
        print("-" * 50)

    print("=" * 80 + "\n")

# 调用打印函数
print_sensitivity_analysis(sensitivity_results)
```

#### 8. 分析敏感性结果并优化公差分配

基于敏感性分析结果，识别关键公差，优化公差分配。

```python
# 敏感性分析结果处理示例（伪代码）
def analyze_sensitivity_results(sensitivity):
    # 找出对性能影响最大的操作数
    critical_operands = []
    for operand in sensitivity.get("operands", []):
        max_effect = 0
        for effect in operand.get("criterion_effects", []):
            effect_magnitude = max(abs(effect['estimated_change_min']), 
                                  abs(effect['estimated_change_max']))
            if effect_magnitude > max_effect:
                max_effect = effect_magnitude
    
        if max_effect > THRESHOLD:  # THRESHOLD是预定义的敏感性阈值
            critical_operands.append({
                "name": operand['name'],
                "effect_magnitude": max_effect
            })
  
    # 按影响大小排序
    critical_operands.sort(key=lambda x: x["effect_magnitude"], reverse=True)
  
    return critical_operands

# 调用分析函数
critical_operands = analyze_sensitivity_results(sensitivity_results)
print("关键公差操作数:")
for i, operand in enumerate(critical_operands):
    print(f"{i+1}. {operand['name']}, 影响大小: {operand['effect_magnitude']:.6e}")
```

敏感性分析是公差优化的基础，通过它可以识别哪些公差对系统性能影响最大，从而有针对性地优化公差设计，平衡制造成本和光学性能。

## 公差分析核心逻辑：基于操作数的方法（更灵活）

1. **添加操作数**

   - 装配公差
   - TEDX
   - TEDY
   - TETX
   - TETY
   - 不规则度公差
   - TEXI (zernike fringe)
2. **仿真设置**

   - 灵敏度模式
   - sensitivity
   - 目标
   - criterion：RMS wavefront
   - sampling: 3/4
   - Comp: None
   - 蒙特卡洛设置
   - save: num
   - File Prefix: xx
3. **运行公差分析**

> 同上，与基于向导的分析方法一致

4. **数据分析**

> 同上，与基于向导的分析方法一致

## 灵敏度模式

### 1. Sensitivity (灵敏度)

* 定义: 该模式用于分析光学系统对输入公差的灵敏度。它评估每个光学元件或表面对系统性能的影响程度。
* 应用: 通过识别哪些元件对系统性能影响最大，设计师可以优先考虑这些元件的制造精度，从而优化整体系统的性能。

### 2. Inverse Limit (反向极限)

* 定义: 该模式用于确定在给定的系统性能要求下，允许的公差范围。它反向计算出在特定性能标准下，元件的公差限制。
* 应用: 适用于需要确保系统性能不低于某一标准的情况，帮助设计师设定合理的制造公差。

### 3. Inverse Increment (反向增量)

* 定义: 该模式用于逐步调整公差，以观察其对系统性能的影响。它通过反向计算，逐步增加公差，直到达到性能要求的边界。
* 应用: 适合于需要精细调整公差以满足特定性能标准的情况，帮助设计师找到最佳的公差设置。

### 4. Skip Sensitivity (跳过灵敏度)

* 定义: 该模式用于跳过灵敏度分析，直接进行其他类型的公差分析。它不考虑元件对系统性能的灵敏度。
* 应用: 当设计师希望快速进行公差分析而不需要灵敏度数据时，可以选择此模式。

## 仿真过程是否采用焦点补偿

"Use Focus Compensation"（使用焦点补偿）

### 情况分析

1. 勾选"Use Focus Compensation"的情况：

- 这模拟了系统在装配后进行**焦点调整**的情况
- 结果将反映系统在**最佳焦点**位置下的性能
- 适合评估系统在实际使用条件下的表现
- 更符合最终产品的实际使用场景（用户可能会调整焦距）

2. 不勾选"Use Focus Compensation"的情况：

- 这模拟了系统没有任何焦点调整的原始状态
- 结果将直接反映**元件偏差**导致的所有影响，包括焦点位移
- 更适合评估元件偏差的"原始"影响
- 适合理解装配误差对系统的直接影响机制

### 推荐策略

1. 两种情况都分析：最理想的方法是**同时进行两种分析**（勾选和不勾选），这样可以全面了解元件偏差的影响：

- **不勾选时**：*了解元件偏差的直接影响*
- **勾选时**：*了解系统调焦后的残余影响*

2. 根据最终产品特性决定：

- 如果您的光学系统允许用户**调焦或有自动对焦**机制，应**优先考虑勾选**
- 如果是**固定焦距**系统且装配后不调整，可能**不勾选**更能反映实际情况
