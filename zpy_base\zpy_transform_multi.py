import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Union, Any, Optional
import random

"""
为多个镜面添加坐标断点并设置偏心倾斜参数，支持批量操作

### 核心逻辑

#### 1. 多镜面数据结构
- `lens_list`: 包含多个镜头信息的列表，每个元素为字典结构
  - `lens_index`: 原始镜面索引（光学系统中的位置）
  - `front_cb_index`: 前坐标断点索引（操作后更新）
  - `rear_cb_index`: 后坐标断点索引（操作后更新）
  - `lens_modified_index`: 镜面修改后的新索引
  - `params`: 偏心倾斜参数字典

#### 2. `add_coordinate_breaks`
该函数为多个镜头批量添加坐标断点，处理流程：
1. **列表排序预处理**：先按`lens_index`升序排序，确保处理顺序正确
2. **动态索引管理**：
   - 维护`index_offset`变量跟踪索引偏移
   - 实际表面索引 = 原始索引 + 当前偏移量
   - 每处理一个镜面，偏移量增加2（因添加两个坐标断点）
3. **批量断点插入**：
   - 前表面位置插入CoordinateBreak类型表面
   - 后表面位置+1处插入CoordinateBreak（补偿前插入导致的索引变化）
4. **间隔调整**：
   - 记录原始镜面间隔，用于后面坐标断点设置
   - 将原始镜面间隔设为0，维持光学路径长度

#### 3. `set_tilt_decenter`
批量设置偏心倾斜参数的核心机制：
1. **参数应用**：遍历`lens_list`，为每个镜面：
   - 前坐标断点：应用指定参数
   - 后坐标断点：应用相反数参数（自动补偿）
2. **顺序控制**：
   - 前断点：按指定order执行操作
   - 后断点：自动使用相反order（1-order）
3. **状态标记**：所有镜面处理完成后标记`transformed`状态

#### 4. 与单镜面版本的关键差异
1. **索引动态计算**：实时跟踪处理过程中的索引变化
2. **批量原子操作**：确保多个镜面的修改作为一个完整事务
3. **参数隔离**：每个镜面参数独立存储和应用
4. **性能优化**：减少Zemax API调用次数

### 总结
通过这种设计，可以高效地为复杂光学系统中的多个镜面同时设置不同的偏心倾斜参数，
同时保持各镜面参数间的独立性和系统的光学性能。
"""

def sort_lens_list(lens_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    对镜头列表按照'lens_index'进行升序排序
    
    Args:
        lens_list (List[Dict[str, Any]]): 镜头信息列表
        
    Returns:
        List[Dict[str, Any]]: 排序后的镜头列表
    """
    return sorted(lens_list, key=lambda x: x["lens_index"])


# 导入PythonStandaloneApplication类
from zpy_base.zpy_connection import PythonStandaloneApplication
# 导入ZemaxWavefrontAnalysis类
from zpy_analysis.zpy_wavefront import ZemaxWavefrontAnalysis

class TransformError(Exception):
    """偏心倾斜操作时的一般错误。"""
    pass

class TransformCreationError(TransformError):
    """创建坐标断点时的错误。"""
    pass

class TransformApplyError(TransformError):
    """应用偏心倾斜时的错误。"""
    pass

class TransformAnalysisError(TransformError):
    """变换后分析时的错误。"""
    pass

class ZemaxTransform:
    """
    用于进行Zemax光学系统偏心倾斜操作的类。
    提供为指定镜头添加坐标断点、设置偏心倾斜参数、应用变换和波前分析功能。
    """

    def __init__(self, zos: PythonStandaloneApplication):
        """
        初始化与Zemax的连接。

        Args:
            zos (PythonStandaloneApplication): Zemax连接实例。

        Raises:
            Exception: 如果初始化失败。
        """
        try:
            # 创建与Zemax的连接
            self.zos = zos
            self.ZOSAPI = self.zos.ZOSAPI
            self.TheApplication = self.zos.TheApplication
            self.TheSystem = self.zos.TheSystem
            
            # 初始化变换相关变量
            self.lens_list = []  # 替代原来的self.coordinate_breaks和self.lens_index
            self.transformed = False
            self.original_file_path = None
        except Exception as e:
            raise Exception(f"Zemax API初始化失败: {str(e)}")

    def load_file(self, file_path: str, save_if_needed: bool = False) -> None:
        """
        加载Zemax文件。

        Args:
            file_path (str): Zemax文件路径。
            save_if_needed (bool, optional): 如果需要，是否保存当前文件。默认为False。

        Raises:
            FileNotFoundError: 如果文件不存在。
            Exception: 如果加载文件时出错。
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            self.TheSystem.LoadFile(file_path, save_if_needed)
            self.original_file_path = file_path
            print(f"文件加载成功: {file_path}")
        except FileNotFoundError as e:
            raise
        except Exception as e:
            raise Exception(f"加载文件时出错: {str(e)}")

    def add_coordinate_breaks(self, lens_list: List[Dict[str, Any]]) -> None:
        """
        为多个指定镜头添加坐标断点。
        
        Args:
            lens_list (List[Dict[str, Any]]): 镜头信息列表，每个元素包含镜头索引和偏心倾斜参数。
            
        Raises:
            TransformCreationError: 如果添加坐标断点时出错。
        """
        try:
            if self.TheSystem is None:
                raise TransformCreationError("系统未初始化，请先加载Zemax文件")
            
            # 存储镜头列表
            self.lens_list = lens_list
            
            # 索引偏移量，用于跟踪由于添加坐标断点导致的后续索引变化
            index_offset = 0
            
            for i, lens_info in enumerate(self.lens_list):
                # 应用索引偏移获取实际表面索引
                original_lens_index = lens_info["lens_index"]
                surface_index = original_lens_index + index_offset
                
                # 获取镜头前后的表面
                front_surface = surface_index
                rear_surface = surface_index + 1

                # 获取镜头更新后的索引
                surface_modified_index = surface_index + 1
                
                print(f"为镜头 {original_lens_index} (实际索引 {surface_modified_index}) 添加坐标断点")
                
                # 在前表面位置插入坐标断点
                cb_front = self.TheSystem.LDE.InsertNewSurfaceAt(front_surface)
                cb_settings = cb_front.GetSurfaceTypeSettings(self.ZOSAPI.Editors.LDE.SurfaceType.CoordinateBreak)
                cb_front.ChangeType(cb_settings)
                
                # 在后表面位置插入坐标断点（+1是因为前面插入了一个表面）
                cb_rear = self.TheSystem.LDE.InsertNewSurfaceAt(rear_surface + 1)
                cb_rear.ChangeType(cb_settings)
                
                # 获取更新后的镜面和坐标断点后面，修改两者的间隔
                cb_rear_surface = self.TheSystem.LDE.GetSurfaceAt(rear_surface + 1)
                modified_surface = self.TheSystem.LDE.GetSurfaceAt(surface_modified_index)
                original_thickness = modified_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Thickness).Value
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Thickness).Value = str(original_thickness)
                modified_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Thickness).Value = str(0)
                
                # 保存坐标断点索引
                self.lens_list[i]["front_cb_index"] = front_surface
                self.lens_list[i]["rear_cb_index"] = rear_surface + 1
                self.lens_list[i]["lens_modified_index"] = surface_modified_index

                # 增加偏移量，每添加一对坐标断点，偏移量增加2
                index_offset += 2
                
                print(f"坐标断点添加成功，前表面: {front_surface}，后表面: {rear_surface + 1}")
                
        except Exception as e:
            raise TransformCreationError(f"添加坐标断点时出错: {str(e)}")

    def set_tilt_decenter(self) -> None:
        """
        根据lens_list中的信息设置多个镜头的偏心倾斜参数。
        
        Raises:
            TransformApplyError: 如果设置偏心倾斜参数时出错。
        """
        try:
            if not self.lens_list:
                raise TransformApplyError("未添加坐标断点，请先调用add_coordinate_breaks方法")
            
            for lens_info in self.lens_list:
                # 获取坐标断点索引
                front_cb_index = lens_info["front_cb_index"]
                rear_cb_index = lens_info["rear_cb_index"]
                
                if front_cb_index is None or rear_cb_index is None:
                    continue  # 跳过未成功添加坐标断点的镜面
                
                # 获取偏心倾斜参数
                params = lens_info["params"]
                x_decenter = params.get("x_decenter", 0.0)
                y_decenter = params.get("y_decenter", 0.0)
                z_decenter = params.get("z_decenter", 0.0)
                x_tilt = params.get("x_tilt", 0.0)
                y_tilt = params.get("y_tilt", 0.0)
                z_tilt = params.get("z_tilt", 0.0)
                order = params.get("order", 0)
                
                # 获取前坐标断点表面
                cb_front_surface = self.TheSystem.LDE.GetSurfaceAt(front_cb_index)
                cb_rear_surface = self.TheSystem.LDE.GetSurfaceAt(rear_cb_index)
                
                # 设置坐标断点参数，后面数值为前面数值的相反数（补偿项）
                # 参数1：X偏心量
                cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par1).Value = str(x_decenter)
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par1).Value = str(-x_decenter)
                # 参数2：Y偏心量
                cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par2).Value = str(y_decenter)
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par2).Value = str(-y_decenter)
                # 参数3：X倾斜角度
                cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par3).Value = str(x_tilt)
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par3).Value = str(-x_tilt)
                # 参数4：Y倾斜角度
                cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par4).Value = str(y_tilt)
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par4).Value = str(-y_tilt)
                # 参数5：Z倾斜角度（旋转角度）
                cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par5).Value = str(z_tilt)
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par5).Value = str(-z_tilt)
                # 参数6：变换顺序
                cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par6).Value = str(order)
                if order == 0:
                    cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par6).Value = str(1)
                else:
                    cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par6).Value = str(0)
                
                print(f"为镜头 {lens_info['lens_index']} 应用偏心倾斜：X偏心={x_decenter}mm, Y偏心={y_decenter}mm, Z偏心={z_decenter}mm, "
                      f"X倾斜={x_tilt}°, Y倾斜={y_tilt}°, Z倾斜={z_tilt}°, 顺序={order}")
            
            # 标记已应用变换
            self.transformed = True
            
        except Exception as e:
            raise TransformApplyError(f"设置偏心倾斜参数时出错: {str(e)}")

    def apply_random_transformation(self, max_decenter: float = 0.1, max_tilt: float = 1.0) -> None:
        """
        为所有镜头应用随机偏心倾斜参数。
        
        Args:
            max_decenter (float, optional): 最大偏心量，单位mm。默认为0.1。
            max_tilt (float, optional): 最大倾斜角度，单位度。默认为1.0。
            
        Raises:
            TransformApplyError: 如果应用随机变换时出错。
        """
        try:
            if not self.lens_list:
                raise TransformApplyError("未添加坐标断点，请先调用add_coordinate_breaks方法")
            
            for i, lens_info in enumerate(self.lens_list):
                # 生成随机偏心倾斜参数
                x_decenter = random.uniform(-max_decenter, max_decenter)
                y_decenter = random.uniform(-max_decenter, max_decenter)
                z_decenter = random.uniform(-max_decenter, max_decenter)
                x_tilt = random.uniform(-max_tilt, max_tilt)
                y_tilt = random.uniform(-max_tilt, max_tilt)
                z_tilt = random.uniform(-max_tilt, max_tilt)
                order = random.randint(0, 1)
                
                # 更新参数字典
                self.lens_list[i]["params"] = {
                    "x_decenter": x_decenter,
                    "y_decenter": y_decenter,
                    "z_decenter": z_decenter,
                    "x_tilt": x_tilt,
                    "y_tilt": y_tilt,
                    "z_tilt": z_tilt,
                    "order": order
                }
            
            # 应用偏心倾斜参数
            self.set_tilt_decenter()
            
        except Exception as e:
            raise TransformApplyError(f"应用随机变换时出错: {str(e)}")

    def run_wavefront_analysis(self) -> Dict[str, Dict[str, float]]:
        """
        运行波前分析，获取PV和RMS值。
        
        Returns:
            Dict[str, Dict[str, float]]: 每个视场每个波长的PV和RMS值。
            
        Raises:
            TransformAnalysisError: 如果运行波前分析时出错。
        """
        try:
            # 创建ZemaxWavefrontAnalysis实例
            wavefront_analyzer = ZemaxWavefrontAnalysis(self.zos)
            
            # 获取结果字典
            results = {}
            
            # 获取当前系统的视场和波长信息
            fields = self.TheSystem.SystemData.Fields.NumberOfFields
            wavelengths = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
            
            print(f"\n======= 波前分析 =======")
            print(f"视场数量: {fields}")
            print(f"波长数量: {wavelengths}")
            
            # 对每个视场和波长进行分析
            for field_index in range(fields):
                field_results = {}
                
                for wavelength_index in range(wavelengths):
                    # 创建波前分析
                    wavefront_analyzer.create_wavefront_analysis(
                        field_number=field_index + 1,
                        wavelength_number=wavelength_index + 1,
                        surface_number=-1,  # 像面
                        use_exit_pupil=True,
                        sampling=64,
                        rotation=0,
                        scale=1.0,
                        polarization="None",
                        reference_to_primary=False,
                        remove_tilt=False
                    )
                    
                    # 运行分析
                    wavefront_analyzer.run_analysis()
                    
                    # 获取结果
                    wavefront_analyzer.get_results()
                    
                    # 提取波前数据
                    wavefront_data = wavefront_analyzer.extract_wavefront_data(
                        field_index=field_index, 
                        wavelength_index=wavelength_index
                    )
                    
                    # 获取RMS和PV
                    rms_error = wavefront_data.get("RMS波前误差", 0.0)
                    pv_error = wavefront_data.get("PV波前误差", 0.0)
                    
                    # 保存结果
                    field_results[f"波长_{wavelength_index+1}"] = {
                        "RMS": rms_error,
                        "PV": pv_error
                    }
                    
                    print(f"视场 {field_index+1}, 波长 {wavelength_index+1}: RMS={rms_error:.6f}, PV={pv_error:.6f}")
                    
                results[f"视场_{field_index+1}"] = field_results
            
            print("=======================")
            
            # 关闭分析器释放资源
            wavefront_analyzer.close()
            
            return results
            
        except Exception as e:
            raise TransformAnalysisError(f"运行波前分析时出错: {str(e)}")

    def analyze_transformed_system(self) -> Dict[str, Any]:
        """
        分析变换后的系统，包括波前和光学性能。
        
        Returns:
            Dict[str, Any]: 分析结果。
            
        Raises:
            TransformAnalysisError: 如果分析变换后的系统时出错。
        """
        try:
            if not self.transformed:
                raise TransformAnalysisError("系统未应用变换，请先调用set_tilt_decenter方法")
            
            # 结果字典
            results = {}
            
            # 运行波前分析
            wavefront_results = self.run_wavefront_analysis()
            results["波前分析"] = wavefront_results
            
            # 计算平均RMS和PV
            total_rms = 0.0
            total_pv = 0.0
            count = 0
            
            for field_key, field_data in wavefront_results.items():
                for wavelength_key, wavelength_data in field_data.items():
                    total_rms += wavelength_data["RMS"]
                    total_pv += wavelength_data["PV"]
                    count += 1
            
            if count > 0:
                avg_rms = total_rms / count
                avg_pv = total_pv / count
            else:
                avg_rms = 0.0
                avg_pv = 0.0
            
            results["平均RMS"] = avg_rms
            results["平均PV"] = avg_pv
            
            print(f"\n系统分析结果:")
            print(f"平均RMS波前误差: {avg_rms:.6f} 波长")
            print(f"平均PV波前误差: {avg_pv:.6f} 波长")
            
            return results
            
        except Exception as e:
            raise TransformAnalysisError(f"分析变换后的系统时出错: {str(e)}")

    def save_transformed_file(self, custom_suffix: str = "trans") -> str:
        """
        保存变换后的文件。
        
        Args:
            custom_suffix (str, optional): 文件名后缀。默认为"trans"。
            
        Returns:
            str: 保存的文件路径。
            
        Raises:
            Exception: 如果保存文件时出错。
        """
        try:
            if not self.original_file_path:
                raise Exception("没有原始文件路径，请先加载文件")
            
            # 构建新文件名
            file_dir, file_name = os.path.split(self.original_file_path)
            name, ext = os.path.splitext(file_name)
            new_file_name = f"{name}_{custom_suffix}{ext}"
            new_file_path = os.path.join(file_dir, new_file_name)
            
            # 保存文件
            self.TheSystem.SaveAs(new_file_path)
            print(f"变换后的文件已保存为: {new_file_path}")
            
            return new_file_path
            
        except Exception as e:
            raise Exception(f"保存变换后的文件时出错: {str(e)}")

    def close(self) -> None:
        """
        关闭变换操作并释放资源。
        """
        try:
            # 释放资源
            if hasattr(self, 'zos') and self.zos is not None:
                del self.zos
                self.zos = None
                
            # 删除其他引用
            if hasattr(self, 'ZOSAPI'):
                self.ZOSAPI = None
            if hasattr(self, 'TheApplication'):
                self.TheApplication = None
            if hasattr(self, 'TheSystem'):
                self.TheSystem = None
                
            print("变换操作已关闭")
        except Exception as e:
            print(f"关闭变换操作时出错: {str(e)}")

    def __del__(self):
        """
        析构函数，确保资源被正确释放。
        """
        self.close()


if __name__ == "__main__":
    """
    主程序入口，演示ZemaxTransform类的用法。
    """
    # 示例文件路径
    sample_file = os.path.abspath(os.path.join("data", "4FAN.zos"))
    
    try:
        # 创建Zemax连接
        zos = PythonStandaloneApplication()
        
        # 创建ZemaxTransform实例
        transform = ZemaxTransform(zos)
        
        # 加载示例文件
        transform.load_file(sample_file)
        
        # 创建镜面列表，为镜面1和2设置偏心倾斜参数
        # lens_list是一个包含多个镜头信息的列表，每个镜头的信息以字典形式存储。
        # 每个字典包含以下键值对：
        # - "lens_index": 镜头的索引，表示该镜头在光学系统中的位置。
        # - "front_cb_index": 前坐标断点的索引，初始值为None，表示尚未设置。
        # - "lens_modified_index": 镜头修改后的索引，初始值为None，表示尚未设置。
        # - "rear_cb_index": 后坐标断点的索引，初始值为None，表示尚未设置。
        # - "params": 一个字典，包含与该镜头相关的偏心倾斜参数，具体包括：
        #   - "x_decenter": X方向的偏心量，单位为米。
        #   - "y_decenter": Y方向的偏心量，单位为米。
        #   - "z_decenter": Z方向的偏心量，单位为米。
        #   - "x_tilt": X方向的倾斜角度，单位为弧度。
        #   - "y_tilt": Y方向的倾斜角度，单位为弧度。
        #   - "z_tilt": Z方向的倾斜角度，单位为弧度。
        #   - "order": 偏心倾斜的顺序，通常用于指定处理的优先级。
        lens_list = [
            {
                "lens_index": 3,
                "front_cb_index": None,
                "lens_modified_index": None,
                "rear_cb_index": None,
                "params": {
                    "x_decenter": 0.005,
                    "y_decenter": 0.007,
                    "z_decenter": 0.0,
                    "x_tilt": 0.005,
                    "y_tilt": 0.003,
                    "z_tilt": 0.0,
                    "order": 0
                }
            },
            {
                "lens_index": 2,
                "front_cb_index": None,
                "lens_modified_index": None,
                "rear_cb_index": None,
                "params": {
                    "x_decenter": 0.008,
                    "y_decenter": 0.004,
                    "z_decenter": 0.0,
                    "x_tilt": 0.006,
                    "y_tilt": 0.004,
                    "z_tilt": 0.0,
                    "order": 0
                }
            }
        ]

        sorted_list = sort_lens_list(lens_list)
        
        # 为镜面1和2添加坐标断点
        transform.add_coordinate_breaks(sorted_list)
        
        # 设置偏心倾斜参数
        transform.set_tilt_decenter()
        
        # 分析变换后的系统
        analysis_results = transform.analyze_transformed_system()
        
        # 保存变换后的文件
        transformed_file_path = transform.save_transformed_file()
        
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 确保资源被释放
        if 'transform' in locals():
            transform.close()
