import os
import sys
from typing import Dict, List, Tuple, Union, Any

# 导入PythonStandaloneApplication类
from zpy_base.zpy_connection import PythonStandaloneApplication

class SurfaceInfoError(Exception):
    """获取表面信息时的一般错误。"""
    pass

class SurfaceTypeError(SurfaceInfoError):
    """获取表面类型时的错误。"""
    pass

class SurfaceRadiusError(SurfaceInfoError):
    """获取曲率半径时的错误。"""
    pass

class SurfaceThicknessError(SurfaceInfoError):
    """获取表面厚度时的错误。"""
    pass

class SurfaceMaterialError(SurfaceInfoError):
    """获取表面材料时的错误。"""
    pass

class SurfaceSemiDiameterError(SurfaceInfoError):
    """获取表面半口径时的错误。"""
    pass

class ZemaxSurfaceInfo:
    """
    用于获取Zemax光学系统中表面信息的类。
    提供获取表面类型、注释、曲率半径、厚度、材料、有效孔径和机械孔径等信息的功能。
    """

    def __init__(self, zos: PythonStandaloneApplication):
        """
        初始化与Zemax的连接。

        Args:
            zos (PythonStandaloneApplication): Zemax连接实例。

        Raises:
            Exception: 如果初始化失败。
        """
        try:
            # 创建与Zemax的连接
            self.zos = zos
            self.ZOSAPI = self.zos.ZOSAPI
            self.TheApplication = self.zos.TheApplication
            self.TheSystem = self.zos.TheSystem
            self.LDE = self.TheSystem.LDE
        except Exception as e:
            raise Exception(f"Zemax API初始化失败: {str(e)}")

    def load_file(self, file_path: str, save_if_needed: bool = False) -> None:
        """
        加载Zemax文件。

        Args:
            file_path (str): Zemax文件路径。
            save_if_needed (bool, optional): 如果需要，是否保存当前文件。默认为False。

        Raises:
            FileNotFoundError: 如果文件不存在。
            Exception: 如果加载文件时出错。
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            self.TheSystem.LoadFile(file_path, save_if_needed)
            print(f"文件加载成功: {file_path}")
        except FileNotFoundError as e:
            raise
        except Exception as e:
            raise Exception(f"加载文件时出错: {str(e)}")

    def get_surface_count(self) -> int:
        """
        获取系统中的表面数量。

        Returns:
            int: 表面数量。

        Raises:
            SurfaceInfoError: 如果获取表面数量时出错。
        """
        try:
            return self.LDE.NumberOfSurfaces
        except Exception as e:
            raise SurfaceInfoError(f"获取表面数量时出错: {str(e)}")

    def get_surface_info(self, surface_index: int) -> Dict[str, Any]:
        """
        获取指定索引表面的详细信息。

        Args:
            surface_index (int): 表面索引，从0开始。

        Returns:
            Dict[str, Any]: 包含表面详细信息的字典。

        Raises:
            SurfaceInfoError: 如果获取表面信息时出错。
        """
        try:
            # 检查索引是否有效
            surface_count = self.get_surface_count()
            if surface_index < 0 or surface_index >= surface_count:
                raise ValueError(f"无效的表面索引: {surface_index}，表面数量: {surface_count}")
            
            # 获取表面
            surface = self.LDE.GetSurfaceAt(surface_index)
            
            # 获取表面类型
            try:
                surface_type = self._get_surface_type_name(surface.Type)
            except Exception as e:
                raise SurfaceTypeError(f"获取表面类型时出错: {str(e)}")
            
            # 获取曲率半径
            try:
                if surface.Radius == 0:
                    radius = float('inf')  # 平面，无穷大半径
                else:
                    radius = surface.Radius
            except Exception as e:
                raise SurfaceRadiusError(f"获取曲率半径时出错: {str(e)}")
            
            # 获取厚度
            try:
                thickness = surface.Thickness
            except Exception as e:
                raise SurfaceThicknessError(f"获取厚度时出错: {str(e)}")
            
            # 获取材料
            try:
                material = surface.Material if surface.Material else ""
            except Exception as e:
                raise SurfaceMaterialError(f"获取材料时出错: {str(e)}")
            
            # 获取半口径
            try:
                semi_dia = surface.SemiDiameter
                mech_semi_dia = surface.MechanicalSemiDiameter
            except Exception as e:
                raise SurfaceSemiDiameterError(f"获取半口径时出错: {str(e)}")
            
            # 获取注释
            try:
                comment = surface.Comment if hasattr(surface, 'Comment') else ""
            except:
                comment = ""
            
            # 获取光阑信息
            is_stop = surface.IsStop
            
            # 创建包含表面信息的字典
            surface_info = {
                "索引": surface_index,
                "表面类型": surface_type,
                "注释": comment,
                "曲率半径": radius,
                "厚度": thickness,
                "材料": material,
                "有效半口径": semi_dia,
                "机械半口径": mech_semi_dia,
                "是否为光阑": is_stop
            }
            
            return surface_info
        except ValueError as e:
            raise
        except SurfaceInfoError as e:
            raise
        except Exception as e:
            raise SurfaceInfoError(f"获取表面信息时出错: {str(e)}")

    def get_all_surfaces_info(self) -> List[Dict[str, Any]]:
        """
        获取所有表面的详细信息。

        Returns:
            List[Dict[str, Any]]: 包含所有表面信息的列表。

        Raises:
            SurfaceInfoError: 如果获取表面信息时出错。
        """
        try:
            surface_count = self.get_surface_count()
            all_surfaces_info = []
            
            for i in range(surface_count):
                try:
                    surface_info = self.get_surface_info(i)
                    all_surfaces_info.append(surface_info)
                except Exception as e:
                    print(f"警告: 获取表面 {i} 信息时出错: {str(e)}")
                    # 添加一个空的或部分信息
                    all_surfaces_info.append({
                        "索引": i,
                        "表面类型": "未知",
                        "注释": "获取信息失败",
                        "曲率半径": None,
                        "厚度": None,
                        "材料": None,
                        "有效半口径": None,
                        "机械半口径": None,
                        "是否为光阑": None
                    })
            
            return all_surfaces_info
        except Exception as e:
            raise SurfaceInfoError(f"获取所有表面信息时出错: {str(e)}")

    def _get_surface_type_name(self, surface_type) -> str:
        """
        私有方法：将Zemax表面类型枚举值转换为可读名称。

        Args:
            surface_type: Zemax表面类型枚举值。

        Returns:
            str: 表面类型的名称。
        """
        surface_types = {
            self.ZOSAPI.Editors.LDE.SurfaceType.Standard: "标准",
            self.ZOSAPI.Editors.LDE.SurfaceType.EvenAspheric: "偶次非球面",
            self.ZOSAPI.Editors.LDE.SurfaceType.OddAsphere: "奇次非球面",
            self.ZOSAPI.Editors.LDE.SurfaceType.Binary1: "二元1",
            self.ZOSAPI.Editors.LDE.SurfaceType.Binary2: "二元2",
            #self.ZOSAPI.Editors.LDE.SurfaceType.GRIN: "梯度折射率",
            self.ZOSAPI.Editors.LDE.SurfaceType.Toroidal: "环面",
            self.ZOSAPI.Editors.LDE.SurfaceType.Biconic: "双锥面",
            #self.ZOSAPI.Editors.LDE.SurfaceType.Zernike: "Zernike",
            #self.ZOSAPI.Editors.LDE.SurfaceType.Gridded: "网格化表面",
            #self.ZOSAPI.Editors.LDE.SurfaceType.FZernikePhase: "F-Zernike相位",
            self.ZOSAPI.Editors.LDE.SurfaceType.CoordinateBreak: "坐标间断",
            self.ZOSAPI.Editors.LDE.SurfaceType.QTypeAsphere: "Q型非球面",
            self.ZOSAPI.Editors.LDE.SurfaceType.ExtendedPolynomial: "扩展多项式"
        }
        return surface_types.get(surface_type, f"未知类型({surface_type})")

    def print_surface_info(self, surface_index: int) -> None:
        """
        打印指定表面的详细信息。

        Args:
            surface_index (int): 表面索引，从0开始。

        Raises:
            SurfaceInfoError: 如果获取表面信息时出错。
        """
        try:
            surface_info = self.get_surface_info(surface_index)
            
            print(f"\n======= 表面 {surface_index} =======")
            print(f"表面类型: {surface_info['表面类型']}")
            if surface_info['注释']:
                print(f"注释: {surface_info['注释']}")
            
            radius_str = "无穷大" if surface_info['曲率半径'] == float('inf') else f"{surface_info['曲率半径']:.6f}"
            print(f"曲率半径: {radius_str}")
            print(f"厚度: {surface_info['厚度']:.6f}")
            
            if surface_info['材料']:
                print(f"材料: {surface_info['材料']}")
            else:
                print("材料: --")
            
            print(f"有效半口径: {surface_info['有效半口径']:.6f}")
            print(f"机械半口径: {surface_info['机械半口径']:.6f}")
            
            if surface_info['是否为光阑']:
                print("此表面为光阑")
            
            print("==============================")
        except Exception as e:
            raise SurfaceInfoError(f"打印表面信息时出错: {str(e)}")

    def print_all_surfaces_info(self) -> None:
        """
        打印所有表面的详细信息。

        Raises:
            SurfaceInfoError: 如果获取表面信息时出错。
        """
        try:
            all_surfaces_info = self.get_all_surfaces_info()
            
            print("\n======= 镜头表面信息 =======")
            print(f"总表面数: {len(all_surfaces_info)}")
            
            for surface_info in all_surfaces_info:
                print(f"\n--- 表面 {surface_info['索引']} ---")
                print(f"表面类型: {surface_info['表面类型']}")
                if surface_info['注释']:
                    print(f"注释: {surface_info['注释']}")
                
                if surface_info['曲率半径'] is not None:
                    radius_str = "无穷大" if surface_info['曲率半径'] == float('inf') else f"{surface_info['曲率半径']:.6f}"
                    print(f"曲率半径: {radius_str}")
                
                if surface_info['厚度'] is not None:
                    print(f"厚度: {surface_info['厚度']:.6f}")
                
                if surface_info['材料'] is not None:
                    if surface_info['材料']:
                        print(f"材料: {surface_info['材料']}")
                    else:
                        print("材料: --")
                
                if surface_info['有效半口径'] is not None:
                    print(f"有效半口径: {surface_info['有效半口径']:.6f}")
                
                if surface_info['机械半口径'] is not None:
                    print(f"机械半口径: {surface_info['机械半口径']:.6f}")
                
                if surface_info['是否为光阑'] is not None and surface_info['是否为光阑']:
                    print("此表面为光阑")
            
            print("\n==============================")
        except Exception as e:
            raise SurfaceInfoError(f"打印所有表面信息时出错: {str(e)}")

    def close(self) -> None:
        """
        关闭与Zemax的连接并释放资源。
        """
        try:
            # 删除对象引用
            if hasattr(self, 'zos') and self.zos is not None:
                del self.zos
                self.zos = None
                
            # 删除其他引用
            if hasattr(self, 'ZOSAPI'):
                self.ZOSAPI = None
            if hasattr(self, 'TheApplication'):
                self.TheApplication = None
            if hasattr(self, 'TheSystem'):
                self.TheSystem = None
            if hasattr(self, 'LDE'):
                self.LDE = None
                
            print("Zemax连接已关闭")
        except Exception as e:
            print(f"关闭Zemax连接时出错: {str(e)}")

    def __del__(self):
        """
        析构函数，确保资源被正确释放。
        """
        self.close()


if __name__ == "__main__":
    """
    主程序入口，演示ZemaxSurfaceInfo类的用法。
    """
    # 示例文件路径
    sample_file = os.path.abspath(os.path.join("data", "4FAN.ZOS"))
    print(f"示例文件路径: {sample_file}")
    
    try:
        # 创建Zemax连接
        zos = PythonStandaloneApplication()
        
        # 创建ZemaxSurfaceInfo实例
        surface_info = ZemaxSurfaceInfo(zos)
        
        # 加载示例文件
        surface_info.load_file(sample_file)
        
        # 获取表面数量
        surface_count = surface_info.get_surface_count()
        print(f"镜头总表面数: {surface_count}")
        
        # 打印所有表面信息
        surface_info.print_all_surfaces_info()
        
        # 打印特定表面信息
        specific_surface = 5  # 第6个表面（索引从0开始）
        print(f"\n获取特定表面（索引 {specific_surface}）的信息:")
        surface_info.print_surface_info(specific_surface)
        
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 确保资源被释放
        if 'surface_info' in locals():
            surface_info.close()
