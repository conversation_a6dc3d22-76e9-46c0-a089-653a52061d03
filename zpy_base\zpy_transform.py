import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Union, Any, Optional
import random

"""
为单个镜面添加坐标断点，然后设置偏心倾斜参数，
给定镜面索引

### 核心逻辑

#### 1. `add_coordinate_breaks`

该函数的主要作用是为指定的镜头添加坐标断点。坐标断点是用于在光学系统中定义偏心和倾斜的关键位置。通过在镜头的前后表面插入坐标断点，可以在后续的操作中对这些表面进行偏心和倾斜的设置。

**核心逻辑**：
- **系统初始化检查**：首先检查光学系统是否已初始化，以确保后续操作的有效性。
- **表面索引计算**：根据给定的镜头索引计算前表面和后表面的索引。
- **镜头索引**：镜头的索引是从0开始的整数，表示在光学系统中镜头的顺序。
- **前表面索引**：前表面的索引直接等于镜头索引，即 `front_surface = lens_index`。
- **后表面索引**：后表面的索引是前表面索引加1，表示在光学系统中后表面紧随前表面之后，因此 `rear_surface = lens_index + 1`。
- **镜头更新后的索引**：镜头更新后的索引是镜头索引加1，表示在光学系统中镜头更新后的索引，因此 `surface_modified_index = lens_index + 1`。(用于调整间隔)
- **清空旧坐标断点**：在添加新的坐标断点之前，清空之前的坐标断点，以避免数据混淆。
- **插入坐标断点**：在前表面和后表面插入新的坐标断点，并将其类型设置为 `CoordinateBreak`，以便后续的偏心和倾斜操作。
- **更新表面厚度**：将更**新后的镜面厚度**设置为0，将后表面厚度设置为**原镜面厚度**，以实现坐标断点与镜面之间的间隔。

#### 2. `set_tilt_decenter`

该函数用于设置镜头的偏心和倾斜参数。通过在之前添加的坐标断点上应用这些参数，可以实现对光学系统的精确调整。

**核心逻辑**：
- **坐标断点检查**：检查是否已添加坐标断点，以确保可以进行偏心和倾斜的设置。
- **获取表面信息**：获取前后坐标断点的表面信息，以便进行参数设置。
- **设置偏心和倾斜参数**：将偏心量和倾斜角度应用于前表面，同时将后表面的参数设置为前表面的**相反数**，以实现补偿效果。
- **标记变换状态**：在成功应用偏心和倾斜参数后，标记变换已应用，以便后续操作可以基于此状态进行。

### 总结

通过这两个函数，用户可以灵活地为光学系统中的镜头设置偏心和倾斜参数，从而实现对光学性能的精确控制。坐标断点的引入使得这些操作更加直观和易于管理。

"""

# 导入PythonStandaloneApplication类
from zpy_base.zpy_connection import PythonStandaloneApplication
# 导入ZemaxWavefrontAnalysis类
from zpy_analysis.zpy_wavefront import ZemaxWavefrontAnalysis

class TransformError(Exception):
    """偏心倾斜操作时的一般错误。"""
    pass

class TransformCreationError(TransformError):
    """创建坐标断点时的错误。"""
    pass

class TransformApplyError(TransformError):
    """应用偏心倾斜时的错误。"""
    pass

class TransformAnalysisError(TransformError):
    """变换后分析时的错误。"""
    pass

class ZemaxTransform:
    """
    用于进行Zemax光学系统偏心倾斜操作的类。
    提供为指定镜头添加坐标断点、设置偏心倾斜参数、应用变换和波前分析功能。
    """

    def __init__(self, zos: PythonStandaloneApplication):
        """
        初始化与Zemax的连接。

        Args:
            zos (PythonStandaloneApplication): Zemax连接实例。

        Raises:
            Exception: 如果初始化失败。
        """
        try:
            # 创建与Zemax的连接
            self.zos = zos
            self.ZOSAPI = self.zos.ZOSAPI
            self.TheApplication = self.zos.TheApplication
            self.TheSystem = self.zos.TheSystem
            
            # 初始化变换相关变量
            self.coordinate_breaks = []
            self.lens_index = None
            self.transformed = False
            self.original_file_path = None
        except Exception as e:
            raise Exception(f"Zemax API初始化失败: {str(e)}")

    def load_file(self, file_path: str, save_if_needed: bool = False) -> None:
        """
        加载Zemax文件。

        Args:
            file_path (str): Zemax文件路径。
            save_if_needed (bool, optional): 如果需要，是否保存当前文件。默认为False。

        Raises:
            FileNotFoundError: 如果文件不存在。
            Exception: 如果加载文件时出错。
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            self.TheSystem.LoadFile(file_path, save_if_needed)
            self.original_file_path = file_path
            print(f"文件加载成功: {file_path}")
        except FileNotFoundError as e:
            raise
        except Exception as e:
            raise Exception(f"加载文件时出错: {str(e)}")

    def add_coordinate_breaks(self, lens_index: int) -> None:
        """
        为指定镜头添加坐标断点。
        
        Args:
            lens_index (int): 镜头编号，从0开始。
            
        Raises:
            TransformCreationError: 如果添加坐标断点时出错。
        """
        try:
            if self.TheSystem is None:
                raise TransformCreationError("系统未初始化，请先加载Zemax文件")
            
            # 存储镜头索引
            self.lens_index = lens_index
            
            # 计算实际表面编号（zemax中表面从0开始）
            surface_index = lens_index
            
            # 获取镜头前后的表面
            front_surface = surface_index
            rear_surface = surface_index + 1

            # 获取镜头更新后的索引
            surface_modified_index = surface_index + 1
            
            # 清空之前的坐标断点
            self.coordinate_breaks = []
            
            print(f"为镜头 {lens_index} 添加坐标断点 (表面 {front_surface} 和 {rear_surface})")
            
            # 在前表面位置插入坐标断点
            cb_front = self.TheSystem.LDE.InsertNewSurfaceAt(front_surface)
            cb_settings = cb_front.GetSurfaceTypeSettings(self.ZOSAPI.Editors.LDE.SurfaceType.CoordinateBreak)
            cb_front.ChangeType(cb_settings)
            self.coordinate_breaks.append(front_surface)
            
            # 在后表面位置插入坐标断点（+1是因为前面插入了一个表面）
            cb_rear = self.TheSystem.LDE.InsertNewSurfaceAt(rear_surface + 1)
            cb_rear.ChangeType(cb_settings)
            self.coordinate_breaks.append(rear_surface + 1)
            
            # 获取更新后的镜面和坐标断点后面，修改两者的间隔
            cb_rear_surface = self.TheSystem.LDE.GetSurfaceAt(self.coordinate_breaks[1])
            modified_surface = self.TheSystem.LDE.GetSurfaceAt(surface_modified_index)
            original_thickness = modified_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Thickness).Value
            cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Thickness).Value = str(original_thickness)
            modified_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Thickness).Value = str(0)

            
            print(f"坐标断点添加成功，前表面: {self.coordinate_breaks[0]}，后表面: {self.coordinate_breaks[1]}")
            
        except Exception as e:
            raise TransformCreationError(f"添加坐标断点时出错: {str(e)}")

    def set_tilt_decenter(self, 
                         x_decenter: float = 0.0, 
                         y_decenter: float = 0.0, 
                         z_decenter: float = 0.0,
                         x_tilt: float = 0.0, 
                         y_tilt: float = 0.0, 
                         z_tilt: float = 0.0,
                         order: int = 0) -> None:
        """
        设置偏心倾斜参数。
        
        Args:
            x_decenter (float, optional): X方向偏心量，单位mm。默认为0.0。
            y_decenter (float, optional): Y方向偏心量，单位mm。默认为0.0。
            z_decenter (float, optional): Z方向偏心量，单位mm。默认为0.0。
            x_tilt (float, optional): X轴倾斜角度，单位度。默认为0.0。
            y_tilt (float, optional): Y轴倾斜角度，单位度。默认为0.0。
            z_tilt (float, optional): Z轴倾斜角度，单位度。默认为0.0。
            order (int, optional): 变换顺序，0表示先偏心后倾斜，1表示先倾斜后偏心。默认为0。
            
        Raises:
            TransformApplyError: 如果设置偏心倾斜参数时出错。
        """
        try:
            if not self.coordinate_breaks:
                raise TransformApplyError("未添加坐标断点，请先调用add_coordinate_breaks方法")
            
            # 获取前坐标断点表面
            cb_front_surface = self.TheSystem.LDE.GetSurfaceAt(self.coordinate_breaks[0])
            cb_rear_surface = self.TheSystem.LDE.GetSurfaceAt(self.coordinate_breaks[1])
            
            # 设置坐标断点参数，后面数值为前面数值的相反数（补偿项）
            # 参数1：X偏心量
            cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par1).Value = str(x_decenter)
            cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par1).Value = str(-x_decenter)
            # 参数2：Y偏心量
            cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par2).Value = str(y_decenter)
            cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par2).Value = str(-y_decenter)
            # 参数3：X倾斜角度
            cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par3).Value = str(x_tilt)
            cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par3).Value = str(-x_tilt)
            # 参数4：Y倾斜角度
            cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par4).Value = str(y_tilt)
            cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par4).Value = str(-y_tilt)
            # 参数5：Z倾斜角度（旋转角度）
            cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par5).Value = str(z_tilt)
            cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par5).Value = str(-z_tilt)
            # 参数6：变换顺序
            cb_front_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par6).Value = str(order)
            if order == 0:
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par6).Value = str(1)
            else:
                cb_rear_surface.GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par6).Value = str(0)
            
            # 标记已应用变换
            self.transformed = True
            
            print(f"已应用偏心倾斜：X偏心={x_decenter}mm, Y偏心={y_decenter}mm, Z偏心={z_decenter}mm, "
                  f"X倾斜={x_tilt}°, Y倾斜={y_tilt}°, Z倾斜={z_tilt}°, 顺序={order}")
            
        except Exception as e:
            raise TransformApplyError(f"设置偏心倾斜参数时出错: {str(e)}")

    def apply_random_transformation(self, 
                                   max_decenter: float = 0.1, 
                                   max_tilt: float = 1.0) -> Dict[str, float]:
        """
        应用随机偏心倾斜参数。
        
        Args:
            max_decenter (float, optional): 最大偏心量，单位mm。默认为0.1。
            max_tilt (float, optional): 最大倾斜角度，单位度。默认为1.0。
            
        Returns:
            Dict[str, float]: 应用的偏心倾斜参数。
            
        Raises:
            TransformApplyError: 如果应用随机变换时出错。
        """
        try:
            if not self.coordinate_breaks:
                raise TransformApplyError("未添加坐标断点，请先调用add_coordinate_breaks方法")
            
            # 生成随机偏心倾斜参数
            x_decenter = random.uniform(-max_decenter, max_decenter)
            y_decenter = random.uniform(-max_decenter, max_decenter)
            z_decenter = random.uniform(-max_decenter, max_decenter)
            x_tilt = random.uniform(-max_tilt, max_tilt)
            y_tilt = random.uniform(-max_tilt, max_tilt)
            z_tilt = random.uniform(-max_tilt, max_tilt)
            order = random.randint(0, 1)
            
            # 应用偏心倾斜参数
            self.set_tilt_decenter(
                x_decenter=x_decenter, 
                y_decenter=y_decenter, 
                z_decenter=z_decenter,
                x_tilt=x_tilt, 
                y_tilt=y_tilt, 
                z_tilt=z_tilt,
                order=order
            )
            
            # 返回应用的参数
            return {
                "x_decenter": x_decenter,
                "y_decenter": y_decenter,
                "z_decenter": z_decenter,
                "x_tilt": x_tilt,
                "y_tilt": y_tilt,
                "z_tilt": z_tilt,
                "order": order
            }
            
        except Exception as e:
            raise TransformApplyError(f"应用随机变换时出错: {str(e)}")

    def run_wavefront_analysis(self) -> Dict[str, Dict[str, float]]:
        """
        运行波前分析，获取PV和RMS值。
        
        Returns:
            Dict[str, Dict[str, float]]: 每个视场每个波长的PV和RMS值。
            
        Raises:
            TransformAnalysisError: 如果运行波前分析时出错。
        """
        try:
            # 创建ZemaxWavefrontAnalysis实例
            wavefront_analyzer = ZemaxWavefrontAnalysis(self.zos)
            
            # 获取结果字典
            results = {}
            
            # 获取当前系统的视场和波长信息
            fields = self.TheSystem.SystemData.Fields.NumberOfFields
            wavelengths = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
            
            print(f"\n======= 波前分析 =======")
            print(f"视场数量: {fields}")
            print(f"波长数量: {wavelengths}")
            
            # 对每个视场和波长进行分析
            for field_index in range(fields):
                field_results = {}
                
                for wavelength_index in range(wavelengths):
                    # 创建波前分析
                    wavefront_analyzer.create_wavefront_analysis(
                        field_number=field_index + 1,
                        wavelength_number=wavelength_index + 1,
                        surface_number=-1,  # 像面
                        use_exit_pupil=True,
                        sampling=64,
                        rotation=0,
                        scale=1.0,
                        polarization="None",
                        reference_to_primary=False,
                        remove_tilt=False
                    )
                    
                    # 运行分析
                    wavefront_analyzer.run_analysis()
                    
                    # 获取结果
                    wavefront_analyzer.get_results()
                    
                    # 提取波前数据
                    wavefront_data = wavefront_analyzer.extract_wavefront_data(
                        field_index=field_index, 
                        wavelength_index=wavelength_index
                    )
                    
                    # 获取RMS和PV
                    rms_error = wavefront_data.get("RMS波前误差", 0.0)
                    pv_error = wavefront_data.get("PV波前误差", 0.0)
                    
                    # 保存结果
                    field_results[f"波长_{wavelength_index+1}"] = {
                        "RMS": rms_error,
                        "PV": pv_error
                    }
                    
                    print(f"视场 {field_index+1}, 波长 {wavelength_index+1}: RMS={rms_error:.6f}, PV={pv_error:.6f}")
                    
                results[f"视场_{field_index+1}"] = field_results
            
            print("=======================")
            
            # 关闭分析器释放资源
            wavefront_analyzer.close()
            
            return results
            
        except Exception as e:
            raise TransformAnalysisError(f"运行波前分析时出错: {str(e)}")

    def analyze_transformed_system(self) -> Dict[str, Any]:
        """
        分析变换后的系统，包括波前和光学性能。
        
        Returns:
            Dict[str, Any]: 分析结果。
            
        Raises:
            TransformAnalysisError: 如果分析变换后的系统时出错。
        """
        try:
            if not self.transformed:
                raise TransformAnalysisError("系统未应用变换，请先调用set_tilt_decenter方法")
            
            # 结果字典
            results = {}
            
            # 运行波前分析
            wavefront_results = self.run_wavefront_analysis()
            results["波前分析"] = wavefront_results
            
            # 计算平均RMS和PV
            total_rms = 0.0
            total_pv = 0.0
            count = 0
            
            for field_key, field_data in wavefront_results.items():
                for wavelength_key, wavelength_data in field_data.items():
                    total_rms += wavelength_data["RMS"]
                    total_pv += wavelength_data["PV"]
                    count += 1
            
            if count > 0:
                avg_rms = total_rms / count
                avg_pv = total_pv / count
            else:
                avg_rms = 0.0
                avg_pv = 0.0
            
            results["平均RMS"] = avg_rms
            results["平均PV"] = avg_pv
            
            print(f"\n系统分析结果:")
            print(f"平均RMS波前误差: {avg_rms:.6f} 波长")
            print(f"平均PV波前误差: {avg_pv:.6f} 波长")
            
            return results
            
        except Exception as e:
            raise TransformAnalysisError(f"分析变换后的系统时出错: {str(e)}")

    def save_transformed_file(self, custom_suffix: str = "trans") -> str:
        """
        保存变换后的文件。
        
        Args:
            custom_suffix (str, optional): 文件名后缀。默认为"trans"。
            
        Returns:
            str: 保存的文件路径。
            
        Raises:
            Exception: 如果保存文件时出错。
        """
        try:
            if not self.original_file_path:
                raise Exception("没有原始文件路径，请先加载文件")
            
            # 构建新文件名
            file_dir, file_name = os.path.split(self.original_file_path)
            name, ext = os.path.splitext(file_name)
            new_file_name = f"{name}_{custom_suffix}{ext}"
            new_file_path = os.path.join(file_dir, new_file_name)
            
            # 保存文件
            self.TheSystem.SaveAs(new_file_path)
            print(f"变换后的文件已保存为: {new_file_path}")
            
            return new_file_path
            
        except Exception as e:
            raise Exception(f"保存变换后的文件时出错: {str(e)}")

    def close(self) -> None:
        """
        关闭变换操作并释放资源。
        """
        try:
            # 释放资源
            if hasattr(self, 'zos') and self.zos is not None:
                del self.zos
                self.zos = None
                
            # 删除其他引用
            if hasattr(self, 'ZOSAPI'):
                self.ZOSAPI = None
            if hasattr(self, 'TheApplication'):
                self.TheApplication = None
            if hasattr(self, 'TheSystem'):
                self.TheSystem = None
                
            print("变换操作已关闭")
        except Exception as e:
            print(f"关闭变换操作时出错: {str(e)}")

    def __del__(self):
        """
        析构函数，确保资源被正确释放。
        """
        self.close()


if __name__ == "__main__":
    """
    主程序入口，演示ZemaxTransform类的用法。
    """
    # 示例文件路径
    sample_file = os.path.abspath(os.path.join("data", "19-zemax.ZMX"))
    
    try:
        # 创建Zemax连接
        zos = PythonStandaloneApplication()
        
        # 创建ZemaxTransform实例
        transform = ZemaxTransform(zos)
        
        # 加载示例文件
        transform.load_file(sample_file)
        
        # 为指定镜头添加坐标断点，索引为1
        lens_index = 1
        transform.add_coordinate_breaks(lens_index)
        
        # 设置偏心倾斜参数
        transform.set_tilt_decenter(
            x_decenter=0.005,  # 0.05mm X偏心
            y_decenter=0.007,  # 0.07mm Y偏心
            z_decenter=0.0,   # 无Z偏心
            x_tilt=0.005,       # 0.5度 X倾斜
            y_tilt=0.003,       # 0.3度 Y倾斜
            z_tilt=0.0,       # 无Z倾斜
            order=0           # 先偏心后倾斜
        )
        
        # 也可以应用随机变换
        # transform.apply_random_transformation(max_decenter=0.1, max_tilt=1.0)
        
        # 分析变换后的系统
        analysis_results = transform.analyze_transformed_system()
        
        # 保存变换后的文件
        transformed_file_path = transform.save_transformed_file()
        
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 确保资源被释放
        if 'transform' in locals():
            transform.close()
