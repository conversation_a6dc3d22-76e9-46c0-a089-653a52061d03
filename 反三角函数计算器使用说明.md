# 反三角函数计算器使用说明

## 📋 程序简介

这是一个功能完整的反三角函数计算器，支持7种常用反三角函数的计算，并提供度分秒转换工具：

1. **反正弦 (arcsin)** - 正弦函数的反函数
2. **反余弦 (arccos)** - 余弦函数的反函数  
3. **反正切 (arctan)** - 正切函数的反函数
4. **反正切2 (arctan2)** - 两参数反正切函数
5. **反正割 (arcsec)** - 正割函数的反函数
6. **反余割 (arccsc)** - 余割函数的反函数
7. **反余切 (arccot)** - 余切函数的反函数
8. **度分秒转换工具** - 角度格式转换

## 🚀 快速开始

### 运行程序
在终端中执行以下命令：
```bash
python inverse_trigonometric_calculator.py
```

### 操作步骤
1. 启动程序后，会显示功能菜单
2. 输入数字 1-7 选择要计算的反三角函数，或输入 8 使用度分秒转换工具
3. 按照提示输入数值
4. 查看计算结果（包含弧度、角度和度分秒三种格式）
5. 按回车键继续或输入 0 退出程序

## 📐 函数详解

### 1. 反正弦 (arcsin)
- **定义域**: [-1, 1]
- **值域**: [-π/2, π/2] 或 [-90°, 90°]
- **说明**: 计算正弦值对应的角度

**示例**:
```
输入: 0.5
输出: arcsin(0.5) = 0.523599 弧度 = 30.000000 度 = 30°0'0.000"
```

### 2. 反余弦 (arccos)
- **定义域**: [-1, 1]
- **值域**: [0, π] 或 [0°, 180°]
- **说明**: 计算余弦值对应的角度

**示例**:
```
输入: 0.5
输出: arccos(0.5) = 1.047198 弧度 = 60.000000 度 = 60°0'0.000"
```

### 3. 反正切 (arctan)
- **定义域**: (-∞, +∞)
- **值域**: (-π/2, π/2) 或 (-90°, 90°)
- **说明**: 计算正切值对应的角度

**示例**:
```
输入: 1
输出: arctan(1) = 0.785398 弧度 = 45.000000 度 = 45°0'0.000"
```

### 4. 反正切2 (arctan2)
- **定义域**: y和x不能同时为0
- **值域**: (-π, π] 或 (-180°, 180°]
- **说明**: 考虑象限的反正切函数，返回从x轴正方向到点(x,y)的角度

**示例**:
```
输入: y=1, x=1
输出: arctan2(1, 1) = 0.785398 弧度 = 45.000000 度 = 45°0'0.000"
```

### 5. 反正割 (arcsec)
- **定义域**: (-∞, -1] ∪ [1, +∞)
- **值域**: [0, π/2) ∪ (π/2, π] 或 [0°, 90°) ∪ (90°, 180°]
- **说明**: 计算正割值对应的角度

**示例**:
```
输入: 2
输出: arcsec(2) = 1.047198 弧度 = 60.000000 度 = 60°0'0.000"
```

### 6. 反余割 (arccsc)
- **定义域**: (-∞, -1] ∪ [1, +∞)
- **值域**: [-π/2, 0) ∪ (0, π/2] 或 [-90°, 0°) ∪ (0°, 90°]
- **说明**: 计算余割值对应的角度

**示例**:
```
输入: 2
输出: arccsc(2) = 0.523599 弧度 = 30.000000 度 = 30°0'0.000"
```

### 7. 反余切 (arccot)
- **定义域**: (-∞, +∞)
- **值域**: (0, π) 或 (0°, 180°)
- **说明**: 计算余切值对应的角度

**示例**:
```
输入: 1
输出: arccot(1) = 0.785398 弧度 = 45.000000 度 = 45°0'0.000"
```

### 8. 度分秒转换工具
提供多种角度格式之间的转换功能：

#### 8.1 十进制度 → 度分秒
将小数形式的角度转换为度分秒格式。

**示例**:
```
输入: 45.5
输出: 45.5° = 45°30'0.000"
```

#### 8.2 度分秒 → 十进制度
将度分秒格式转换为十进制度数。

**示例**:
```
输入: 45度30分0秒
输出: 45°30'0" = 45.500000°
```

#### 8.3 弧度 → 度分秒
将弧度直接转换为度分秒格式。

**示例**:
```
输入: 0.785398 弧度
输出: 0.785398 弧度 = 45°0'0.000"
```

#### 8.4 度分秒 → 弧度
将度分秒格式转换为弧度。

**示例**:
```
输入: 45度0分0秒
输出: 45°0'0" = 0.785398 弧度
```

## 💡 使用技巧

### 输入提示
- 程序会自动验证输入范围，确保数值在有效定义域内
- 如果输入超出范围，程序会提示错误并要求重新输入
- 在任何输入时，可以输入 `q` 返回主菜单

### 快捷操作
- 使用 `Ctrl+C` 可以中断当前计算并返回主菜单
- 输入 `0` 可以退出程序
- 每次计算后按回车键继续

### 错误处理
- ✅ 自动检测输入值是否在有效范围内
- ✅ 处理除零错误
- ✅ 处理非数字输入
- ✅ 提供清晰的错误提示信息

## 📝 注意事项

1. **定义域限制**: 
   - `arcsin` 和 `arccos` 只接受 [-1, 1] 范围内的值
   - `arcsec` 和 `arccsc` 只接受 |x| ≥ 1 的值

2. **角度单位**: 
   - 程序同时显示弧度、十进制度和度分秒三种结果
   - 弧度是数学中的标准单位
   - 十进制度是日常生活中更常用的单位
   - 度分秒是高精度测量中常用的格式

3. **精度**: 
   - 计算结果保留6位小数
   - 使用Python内置的math库，保证计算精度

4. **特殊情况**:
   - `arctan2(0, 0)` 是未定义的，程序会提示错误
   - 对于 `arccot(0)`，结果是 π/2 弧度（90度）

5. **度分秒格式说明**:
   - 度分秒格式：度°分'秒"
   - 1度 = 60分，1分 = 60秒
   - 例如：45.5° = 45°30'0.000"
   - 负角度在度数前显示负号
   - 秒数精确到小数点后3位

## 🔧 系统要求

- Python 3.x
- 无需额外安装依赖包（使用标准库）

## 📞 技术支持

如果在使用过程中遇到问题，可以：
1. 检查输入值是否在有效范围内
2. 确认Python环境是否正确配置
3. 查看错误提示信息获取帮助

---

**祝您使用愉快！** 🎉 