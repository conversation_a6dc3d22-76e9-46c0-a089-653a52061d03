import os
import sys
from typing import Dict, List, Tuple, Union, Any

# 导入PythonStandaloneApplication类
from zpy_base.zpy_connection import PythonStandaloneApplication

class ZOSAPIError(Exception):
    """Zemax API相关异常（如初始化失败）。"""
    pass

class FileLoadError(Exception):
    """文件加载失败异常。"""
    pass

class GetApertureError(Exception):
    """获取孔径信息失败异常。"""
    pass

class GetFieldError(Exception):
    """获取视场信息失败异常。"""
    pass

class GetWavelengthError(Exception):
    """获取波长信息失败异常。"""
    pass

class GetSystemError(Exception):
    """获取系统信息失败异常。"""
    pass

class ZemaxSystemInfo:
    """
    用于获取Zemax光学系统信息的类。
    提供获取孔径、视场、波长等系统信息的功能。
    """

    def __init__(self, zos: PythonStandaloneApplication):
        """
        初始化与Zemax的连接并可选择性地加载文件。

        Args:
            file_path (str, optional): Zemax文件路径。默认为None。

        Raises:
            Exception: 如果连接Zemax或加载文件时出错。
        """
        try:
            # 创建与Zemax的连接
            self.zos = zos
            self.ZOSAPI = self.zos.ZOSAPI
            self.TheApplication = self.zos.TheApplication
            self.TheSystem = self.zos.TheSystem
                            
        except Exception as e:
            raise ZOSAPIError(f"Zemax API初始化失败: {str(e)}")

    def load_file(self, file_path: str, save_if_needed: bool = False) -> None:
        """
        加载Zemax文件。

        Args:
            file_path (str): Zemax文件路径。
            save_if_needed (bool, optional): 如果需要，是否保存当前文件。默认为False。

        Raises:
            FileNotFoundError: 如果文件不存在。
            Exception: 如果加载文件时出错。
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            self.TheSystem.LoadFile(file_path, save_if_needed)
            print(f"文件加载成功: {file_path}")
        except FileNotFoundError as e:
            raise
        except Exception as e:
            raise FileLoadError(f"加载文件时出错: {str(e)}")

    def get_aperture_info(self) -> Dict[str, Any]:
        """
        获取系统孔径信息。

        Returns:
            Dict[str, Any]: 包含孔径类型、值等信息的字典。

        Raises:
            Exception: 如果获取孔径信息时出错。
        """
        try:
            # 获取系统数据和孔径设置
            system_data = self.TheSystem.SystemData
            aperture = system_data.Aperture
            
            # 创建字典存储孔径信息
            aperture_info = {
                "类型": self._get_aperture_type_name(aperture.ApertureType),
                "值": aperture.ApertureValue,
                "无焦像空间": aperture.AFocalImageSpace
            }
            
            return aperture_info
        except Exception as e:
            raise GetApertureError(f"获取孔径信息时出错: {str(e)}")

    def get_field_info(self) -> Dict[str, Any]:
        """
        获取系统视场信息。

        Returns:
            Dict[str, Any]: 包含视场类型、点数和各视场点信息的字典。

        Raises:
            Exception: 如果获取视场信息时出错。
        """
        try:
            # 获取系统数据和视场设置
            system_data = self.TheSystem.SystemData
            fields = system_data.Fields
            
            # 获取视场类型
            field_type = self._get_field_type_name(fields.GetFieldType())
            
            # 获取视场点数
            field_count = fields.NumberOfFields
            
            # 获取每个视场点的信息
            field_points = []
            for i in range(1, field_count + 1):
                field = fields.GetField(i)
                field_points.append({
                    "编号": i,
                    "X": field.X,
                    "Y": field.Y,
                    "权重": field.Weight,
                    "视场类型": field_type
                })
            
            # 创建字典存储视场信息
            field_info = {
                "类型": field_type,
                "点数": field_count,
                "视场点": field_points
            }
            
            return field_info
        except Exception as e:
            raise GetFieldError(f"获取视场信息时出错: {str(e)}")

    def get_wavelength_info(self) -> Dict[str, Any]:
        """
        获取系统波长信息。

        Returns:
            Dict[str, Any]: 包含波长数量和各波长值的字典。

        Raises:
            Exception: 如果获取波长信息时出错。
        """
        try:
            # 获取系统数据和波长设置
            system_data = self.TheSystem.SystemData
            wavelengths = system_data.Wavelengths
            
            # 获取波长数量
            wave_count = wavelengths.NumberOfWavelengths
            
            # 获取主波长索引
            primary_wave_index = 0
            for i in range(1, wave_count + 1):
                if wavelengths.GetWavelength(i).IsPrimary:
                    primary_wave_index = i
                    break
            
            # 获取每个波长的信息
            wave_values = []
            for i in range(1, wave_count + 1):
                wave = wavelengths.GetWavelength(i)
                wave_values.append({
                    "编号": i,
                    "波长值(μm)": wave.Wavelength,
                    "权重": wave.Weight,
                    "是否为主波长": wave.IsPrimary
                })
            
            # 创建字典存储波长信息
            wavelength_info = {
                "波长数量": wave_count,
                "主波长索引": primary_wave_index,
                "波长列表": wave_values
            }
            
            return wavelength_info
        except Exception as e:
            raise GetWavelengthError(f"获取波长信息时出错: {str(e)}")

    def get_system_info(self) -> Dict[str, Any]:
        """
        获取完整的系统信息。

        Returns:
            Dict[str, Any]: 包含孔径、视场和波长信息的完整系统信息字典。

        Raises:
            Exception: 如果获取系统信息时出错。
        """
        try:
            # 获取各部分信息
            aperture_info = self.get_aperture_info()
            field_info = self.get_field_info()
            wavelength_info = self.get_wavelength_info()
            
            # 获取系统数据
            system_data = self.TheSystem.SystemData
            
            # 获取系统类型
            system_mode = "顺序" if self.TheSystem.Mode == self.ZOSAPI.SystemType.Sequential else "非顺序"
            
            # 获取表面数量
            surface_count = self.TheSystem.LDE.NumberOfSurfaces
            
            # 创建字典存储系统信息
            system_info = {
                "文件名": self.TheSystem.SystemFile,
                "系统类型": system_mode,
                "表面数量": surface_count,
                "孔径信息": aperture_info,
                "视场信息": field_info,
                "波长信息": wavelength_info,
                "标题": system_data.TitleNotes.Title,
                "注释": system_data.TitleNotes.Notes
            }
            
            return system_info
        except Exception as e:
            raise GetSystemError(f"获取系统信息时出错: {str(e)}")

    def _get_aperture_type_name(self, aperture_type) -> str:
        """
        私有方法：将Zemax孔径类型枚举值转换为可读名称。

        Args:
            aperture_type: Zemax孔径类型枚举值。

        Returns:
            str: 孔径类型的名称。

        Note:
            此方法仅供类内部使用，外部不应直接调用。
        """
        aperture_types = {
            self.ZOSAPI.SystemData.ZemaxApertureType.EntrancePupilDiameter: "入瞳直径",
            self.ZOSAPI.SystemData.ZemaxApertureType.ImageSpaceFNum: "像空间F数",
            self.ZOSAPI.SystemData.ZemaxApertureType.ObjectSpaceNA: "光阑浮动",
            self.ZOSAPI.SystemData.ZemaxApertureType.FloatByStopSize: "物空间数值孔径",
            self.ZOSAPI.SystemData.ZemaxApertureType.ParaxialWorkingFNum: "近轴工作F数",
            self.ZOSAPI.SystemData.ZemaxApertureType.ObjectConeAngle: "物锥角"
        }
        return aperture_types.get(aperture_type, "未知孔径类型")

    def _get_field_type_name(self, field_type) -> str:
        """
        私有方法：将Zemax视场类型枚举值转换为可读名称。

        Args:
            field_type: Zemax视场类型枚举值。

        Returns:
            str: 视场类型的名称。

        Note:
            此方法仅供类内部使用，外部不应直接调用。
        """
        field_types = {
            self.ZOSAPI.SystemData.FieldType.Angle: "角度",
            self.ZOSAPI.SystemData.FieldType.ObjectHeight: "物高",
            self.ZOSAPI.SystemData.FieldType.ParaxialImageHeight: "近轴像高",
            self.ZOSAPI.SystemData.FieldType.RealImageHeight: "实际像高"
        }
        return field_types.get(field_type, "未知视场类型")

    def print_system_info(self) -> None:
        """
        打印系统信息。

        Raises:
            Exception: 如果打印系统信息时出错。
        """
        try:
            system_info = self.get_system_info()
            
            print("\n======= Zemax系统信息 =======")
            print(f"文件名: {system_info['文件名']}")
            print(f"系统类型: {system_info['系统类型']}")
            print(f"表面数量: {system_info['表面数量']}")
            print(f"标题: {system_info['标题']}")
            
            # 打印孔径信息
            print("\n--- 孔径信息 ---")
            aperture_info = system_info['孔径信息']
            print(f"类型: {aperture_info['类型']}")
            print(f"值: {aperture_info['值']}")
            print(f"无焦像空间: {aperture_info['无焦像空间']}")
            
            # 打印视场信息
            print("\n--- 视场信息 ---")
            field_info = system_info['视场信息']
            print(f"类型: {field_info['类型']}")
            print(f"点数: {field_info['点数']}")
            print("\n视场点:")
            for point in field_info['视场点']:
                print(f"  编号: {point['编号']}, X: {point['X']}, Y: {point['Y']}, 权重: {point['权重']}")
            
            # 打印波长信息
            print("\n--- 波长信息 ---")
            wavelength_info = system_info['波长信息']
            print(f"波长数量: {wavelength_info['波长数量']}")
            print(f"主波长索引: {wavelength_info['主波长索引']}")
            print("\n波长列表:")
            for wave in wavelength_info['波长列表']:
                primary = "是" if wave['是否为主波长'] else "否"
                print(f"  编号: {wave['编号']}, 波长值: {wave['波长值(μm)']} μm, 权重: {wave['权重']}, 主波长: {primary}")
            
            print("\n==============================")
        except Exception as e:
            raise Exception(f"打印系统信息时出错: {str(e)}")

    def close(self) -> None:
        """
        关闭与Zemax的连接并释放资源。
        """
        try:
            # 删除对象引用
            if hasattr(self, 'zos') and self.zos is not None:
                del self.zos
                self.zos = None
                
            # 删除其他引用
            if hasattr(self, 'ZOSAPI'):
                self.ZOSAPI = None
            if hasattr(self, 'TheApplication'):
                self.TheApplication = None
            if hasattr(self, 'TheSystem'):
                self.TheSystem = None
                
            print("Zemax连接已关闭")
        except Exception as e:
            print(f"关闭Zemax连接时出错: {str(e)}")

    def __del__(self):
        """
        析构函数，确保资源被正确释放。
        """
        self.close()


if __name__ == "__main__":
    """
    主程序入口，演示ZemaxSystemInfo类的用法。
    """
    # 示例文件路径
    sample_file = os.path.abspath(os.path.join("data", "19-zemax.ZMX"))
    print(sample_file)
    
    try:
        zos = PythonStandaloneApplication()

        # 创建ZemaxSystemInfo实例
        zemax_info = ZemaxSystemInfo(zos)
        
        # 加载示例文件
        zemax_info.load_file(sample_file)
        #zemax_info.load_file("D:/code/zemax/zemax_py_tools/data/19-zemax.ZMX")
        #zemax_info.load_file("C:/Users/<USER>/Documents/Zemax/Samples/Sequential/Objectives/Double Gauss 28 degree field.zmx")
        
        # 打印系统信息
        zemax_info.print_system_info()
        
        # 也可以单独获取各部分信息
        # aperture_info = zemax_info.get_aperture_info()
        # field_info = zemax_info.get_field_info()
        # wavelength_info = zemax_info.get_wavelength_info()
        
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 确保资源被释放
        if 'zemax_info' in locals():
            zemax_info.close()
