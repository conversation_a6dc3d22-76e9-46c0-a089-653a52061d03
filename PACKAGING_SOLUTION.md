# 次镜垫片计算器打包问题解决方案

## 问题描述

在使用 PyInstaller 打包 Python 应用时遇到以下错误：
```
System.IO.FileLoadException: 未能加载文件或程序集"file:///D:\Program\github_project\zemax_py_tools\ZPY_ZF\ZPY_ZF\Python.Runtime.dll"或它的某一个依赖项。
```

## 问题分析

### 根本原因
1. **缺少 pythonnet 依赖**: 项目使用 Python.NET 与 ZEMAX 的 .NET API 交互，但依赖列表不完整
2. **打包配置不当**: PyInstaller 无法正确识别和包含 .NET 相关的依赖文件  
3. **安全策略限制**: .NET Framework 对从网络位置加载程序集有安全限制

### 技术背景
- 项目是基于 PyQt5 的光学系统分析工具
- 需要与 ZEMAX OpticStudio 软件集成
- 使用 `clr` 模块（pythonnet）进行 .NET 互操作

## 解决方案

### 1. 更新依赖列表

已更新 `requirements.txt` 文件，添加了必要的依赖：

```txt
PyQt5>=5.15.0
PyQt5-tools>=5.15.0
pythonnet>=3.0.0
pyinstaller>=5.0.0
pywin32>=300
```

### 2. 专用打包脚本

创建了 `build_script.py` 专用打包脚本，具有以下功能：

#### 核心特性
- **自动检测 ZEMAX 安装**: 通过注册表查找 ZEMAX 安装路径
- **收集 .NET 依赖**: 自动收集 ZOSAPI 相关的 DLL 文件
- **生成专用配置**: 创建针对项目的 PyInstaller 配置文件
- **解决安全限制**: 生成运行时配置文件解决 .NET 安全策略问题

#### 主要功能模块
```python
# 1. ZEMAX 依赖检测
def find_zemax_installation()
def collect_zemax_dependencies()

# 2. 打包配置生成
def create_spec_file()

# 3. 安全配置
def create_runtime_config()
```

### 3. 使用方法

#### 步骤1: 安装更新的依赖
```bash
pip install -r requirements.txt
```

#### 步骤2: 运行专用打包脚本
```bash
python build_script.py
```

#### 步骤3: 验证打包结果
打包完成后，在 `dist` 目录中找到 `SecondLensSpacerCalculator.exe`

### 4. 打包配置详解

#### PyInstaller 配置关键点
```python
# 隐藏导入 - 确保所有必要模块被包含
hiddenimports=[
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.uic',
    'clr',                    # Python.NET 核心模块
    'pythonnet',              # Python.NET 包
    'zpy_base.zpy_connection', # ZEMAX 连接模块
    # ... 其他 zpy_base 模块
]

# 数据文件 - 包含 UI 和资源文件
datas=[
    ('Ui', 'Ui'),
    ('resource', 'resource'),
    ('zpy_base', 'zpy_base'),
    # ZEMAX DLL 文件自动添加
]
```

#### 运行时配置
```xml
<!-- SecondLensSpacerCalculator.exe.config -->
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
    </startup>
    <runtime>
        <loadFromRemoteSources enabled="true" />
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <probing privatePath="zemax_libs" />
        </assemblyBinding>
    </runtime>
</configuration>
```

## 部署要求

### 目标机器要求
1. **操作系统**: Windows 10/11 (64位)
2. **.NET Framework**: 4.5 或更高版本
3. **ZEMAX OpticStudio**: 如需完整功能支持（可选）

### 部署注意事项
1. **杀毒软件**: 可能误报，需要添加白名单
2. **防火墙**: 确保应用可以访问网络（如果需要）
3. **权限**: 可能需要管理员权限运行

## 常见问题及解决方案

### Q1: 打包后仍然报错 "未找到 ZEMAX"
**解决方案**: 
- 检查目标机器是否安装了 ZEMAX OpticStudio
- 确保 ZEMAX 正确注册（重新安装或修复）

### Q2: 运行时提示 "pythonnet 初始化失败"
**解决方案**:
- 确保 .NET Framework 已安装
- 检查运行时配置文件是否存在
- 尝试以管理员身份运行

### Q3: 打包体积过大
**解决方案**:
- 使用 `--exclude-module` 排除不必要的模块
- 启用 UPX 压缩（已在脚本中启用）

### Q4: 某些功能在打包后不工作
**解决方案**:
- 检查资源文件路径是否正确
- 确保所有数据文件已包含在打包中
- 使用 `--debug` 模式排查问题

## 替代方案

如果上述方案仍有问题，可考虑：

### 1. 使用 cx_Freeze
```bash
pip install cx_freeze
```

### 2. 使用 Nuitka
```bash
pip install nuitka
```

### 3. 容器化部署
使用 Docker 容器化应用，避免依赖问题

## 技术支持

如遇到其他问题，请提供：
1. 完整的错误日志
2. 系统环境信息
3. ZEMAX 版本信息
4. Python 版本和已安装包列表

---

**最后更新**: 2024年12月
**适用版本**: Python 3.8+, PyQt5, ZEMAX OpticStudio 