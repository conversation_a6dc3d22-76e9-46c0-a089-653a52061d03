# 开发模式

## 提示词

> [目标]，具备如下功能：[功能，列表描述]
> 当需要编写与zemax相关的代码时，必须先从 [json]文件进行查找。[如果可以，手动add相关JSON代码]
> [参考已有代码风格]
> [系统性思维MCP]
> [基于mdc规则]

```markdown
帮我写一个波前分析（wavefront map）的脚本，具备如下功能：（1）创建波前分析；（2）读取分析结果；（3）提取结果相关信息并进行结构化输出；（4）对分析结果进行可视化绘制。

当需要编写与zemax相关的代码时，必须先从 **@zos_api1.json** @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，代码格式参考  **@zpy_surfaceinfo.py**

必须基于sequential thinking MCP服务进行回答

将代码写入 **@zpy_wavefront.py**

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc
```

```makrdown
帮我写一个FFT MTF分析的脚本，具备如下功能：（1）创建FFT MTF分析；（2）读取分析结果；（3）提取结果相关信息并进行结构化输出；（4）对分析结果进行可视化绘制。

当需要编写与zemax相关的代码时，必须先从 **@zos_api1.json** @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，代码格式参考  **@zpy_wavefront.py**

必须基于sequential thinking MCP服务进行回答

将代码写入 **@zpy_fftmtf.py**

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc
```

```markdown
帮我写一个Enclosed Energy分析的脚本，具备如下功能：（1）创建Enclosed Energy(DiffractionEncircledEnergy)分析；（2）读取分析结果，数据类型应该是“IAR_DataSeries”，处理逻辑参考 @zpy_fftmtf.py 的“extract_mtf_data”方法；（3）提取结果相关信息并进行结构化输出；（4）对分析结果进行可视化绘制,参考提供的图片进行绘图。构建完整的类和main函数，测试数据与@zpy_fftmtf.py 中的一致

当需要编写与zemax相关的代码时，必须先从 @zos_api1.json @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，代码格式参考 @zpy_fftmtf.py 

必须基于sequential thinking MCP服务进行回答

将代码写入 @zpy_enclosed_energy.py 

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc      @analysis 
```

```markdown
帮我写一个zernike fringe coefficients分析的脚本，具备如下功能：（1）创建zernike fringe分析（基于ZOSAPI.Analysis.Settings.Aberrations.IAS_ZernikeFringeCoefficients），可以设置sampling、波长、视场、zernike分解最大项；（2）读取分析结果（仅通过`GetResults()`函数获取结果，不要对结果进行处理，等我调试后判断结果的类型再开发后续代码）；（3）将分析结果保存（参考`save_results`相关代码）。

当需要编写与zemax相关的代码时，必须先从 **@zos_api1.json** @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，波长、视场相关代码参考  **@zpy_wavefront.py**

将代码写入 **@zpy_zernike_coeff.py**

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc

参考我提供的JSON代码片段。

按照PLAN-EXECUTE-REVIEW的模式回答。
```

```markdown
帮我写一个zernike fringe coefficients filed分析的脚本，能够分析视场下的zernike 系数，具备如下功能：（1）创建zernike fringe coefficients filed分析（基于ZOSAPI.Analysis.Settings.Aberrations.IAS_ZernikeCoefficientsVsField, 分析变量是ZernikeCoefficientsVsField），可以设置sampling、波长、视场扫描方向（FieldScanDirection）、zernike分解类型（ZernikeCoefficientType）、视场密度（FieldDensity 默认20）；（2）读取分析结果（仅通过`GetResults()`函数获取结果，不要对结果进行处理，等我调试后判断结果的类型再开发后续代码）；（3）将分析结果保存（参考`save_results`相关代码）。

当需要编写与zemax相关的代码时，必须先从 **@zos_api1.json** @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，波长、视场相关代码参考  **@zpy_wavefront.py**

将代码写入 **@zpy_zernike_coeff_field.py**

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc

参考我提供的JSON代码片段。

按照PLAN-EXECUTE-REVIEW的模式回答。
```


```markdown
帮我写一个zemax standard sopt diagram分析的脚本，能够分析标准点列图，具备如下功能：（1）创建zernike standard sopt diagram分析（基于ZOSAPI.Analysis.Settings.Spot.IAS_Spot, 分析变量是StandardSpot），可以设置视场、像面、波长、模式、参考、光线密度等，相关参数的默认设置参考我给定的图片；（2）读取分析结果，通过`GetResults()`函数获取结果，不要对结果进行处理，等我调试后判断结果的类型再开发后续代码）；（3）将分析结果保存（参考`save_results`相关代码）。

当需要编写与zemax相关的代码时，必须先从 **@zos_api1.json** @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，波长、视场相关代码参考  **@zpy_wavefront.py**

将代码写入 **@zpy_spot_standard.py**

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc

参考我提供的JSON代码片段。

按照PLAN-EXECUTE-REVIEW的模式回答。
```

```markdown
帮我写一个ray trace分析的脚本，能够进行光线追迹，并可视化，具备如下功能：（1）打开ray trace工具（ZOSAPI.Tools.IOpticalSystemTools, OpenBatchRayTrace），参考我给定的图片和JSON代码进行参数设置，重点设置光线数量；（2）获取系统的波长数量和视场数量，用于后续可视化，需要对视场进行归一化处理；（3）进行绘制，根据设定的视场和波长进行绘制，如果视场=0，则绘制全部视场（采用1*n的布局），否则仅绘制指定视场，如果波长=0，则在每个图上绘制全部波长的点列图，否则仅绘制单个波长；（4）不同波长绘制需要用不同的颜色进行区分。

当需要编写与zemax相关的代码时，必须先从 **@zos_api1.json** @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，波长、视场相关代码参考  **@zpy_wavefront.py**

将代码写入 **@zpy_raytrace.py**

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc

参考我提供的JSON代码片段。

按照PLAN-EXECUTE-REVIEW的模式回答。
```

```markdown
帮我写一个full field aberration分析的脚本，能够进行全视场像差分析，并可视化，具备如下功能：（1）创建full field aberration分析（基于ZOSAPI.Analysis.Settings.Aberrations.IAS_FullFieldAberration, 分析变量是FullFieldAberration），可以设置相关参数，相关参数的默认设置参考我给定的图片；（2）读取分析结果，通过`GetResults()`函数获取结果，不要对结果进行处理，等我调试后判断结果的类型再开发后续代码）；（3）将分析结果保存（参考`save_results`相关代码）。

当需要编写与zemax相关的代码时，必须先从 **@zos_api1.json** @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，波长、视场相关代码参考  **@zpy_wavefront.py**

将代码写入 @zpy_full_aberration.py 

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc

参考我提供的JSON代码片段。

按照PLAN-EXECUTE-REVIEW的模式回答。
```

```makrdown
@zpy_full_aberration.py 中的“self.aberration_results”是DataGrid数据类型(ZOSAPI.Analysis.Data.IAR_DataGrid)，参考 @zpy_wavefront.py 中的DataGrid数据处理和绘制方法。结合我提供给你的图片和JSON代码片段。

你可能会得到2个DataGrid数据（NumberOfDataGrids = 2），第一个表示像差的幅值（Magnitude），第二个表示像差的相位（Angle），我希望可以绘制FalseColor图的效果（根据XFieldSampling和YFieldSampling创建矩形区域进行颜色映射），如图所示（只需要用到Magnitude，使用强度最小值和最大值进行颜色映射，无效值进行透明处理）。

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc

参考我提供的JSON代码片段。

按照PLAN-EXECUTE-REVIEW的模式回答。

```

```markdown
帮我写一个zemax镜头偏心倾斜的脚本，具备如下功能：（1）引入 @zpy_connection.py 进行zemax连接操作；（2）读取zemax文件；（3）用户会指定镜头编号（zemax的镜头编号从0开始，保持一致）$id$，然后采用坐标断点（coordinate break）的方式为$id$镜头设置坐标断点， $id$之前和之后分别增加一个coordinate break类型；（4）引入 @zpy_wavefront.py 进行分析，输出PV和RMS；（5）保存坐标变换后的模型，保存在输入模型相同目录下，给文件名增加后缀"trans"。构建完整的类和main函数，测试数据与 @zpy_wavefront.py 中的一致

当需要编写与zemax相关的代码时，必须先从 @zos_api1.json @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，代码格式参考 @zpy_wavefront.py 

必须基于sequential thinking MCP服务进行回答

将代码写入  @zpy_transform.py 

基于  @pyzemax-code-master.mdc  @pyzemax-code-style.mdc   

坐标变换逻辑参考如下代码
"if __name__ == '__main__':
    zos = PythonStandaloneApplication()
  
    # load local variables
    ZOSAPI = zos.ZOSAPI
    TheApplication = zos.TheApplication
    TheSystem = zos.TheSystem
    FilePath = TheApplication.ZemaxDataDir + r"\Samples\Sequential\Objectives\Cooke 40 degree field.zos"
    TheSystem.LoadFile(FilePath, False)
  
    # ! [e07s01_py]
    # run the design lockdown tool to fix diameters, remove solves
    LockdownTool = TheSystem.Tools.OpenDesignLockdown()
    LockdownTool.UsePrecisionRounding = True
    LockdownTool.DecimalPrecision = 2
    LockdownTool.RunAndWaitForCompletion()
    LockdownTool.Close()
    # ! [e07s01_py]
  
    # recreate the functionality of the tilt/decenter elements tool
    # apply coordinate breaks around the 2nd lens element (surf 3/4)
    surf3 = TheSystem.LDE.InsertNewSurfaceAt(3)
    surf6 = TheSystem.LDE.InsertNewSurfaceAt(6)
    CB = surf3.GetSurfaceTypeSettings(ZOSAPI.Editors.LDE.SurfaceType.CoordinateBreak)
    surf3.ChangeType(CB)
    surf6.ChangeType(CB)
    # insert a dummy surface after 2nd CB
    surf7 = TheSystem.LDE.InsertNewSurfaceAt(7)
    surf7.Thickness = TheSystem.LDE.GetSurfaceAt(5).Thickness # the dummy carries the original thickness
  
    # ! [e07s02_py]
    # we're going to play with the STOP surface position, so let's put STOP on surf 1
    TheSystem.LDE.GetSurfaceAt(1).IsStop = True
    # ! [e07s02_p]
  
    # ! [e07s03_py]
    # create position solve
    PositionSolve = TheSystem.LDE.GetSurfaceAt(5).ThicknessCell.CreateSolveType(ZOSAPI.Editors.SolveType.Position)
    # set the properties for the position solve
    # solve-specific properties are in ISolvePosition interface, accessed via _S_Position
    PositionSolve._S_Position.FromSurface = 3
    PositionSolve._S_Position.Length = 0
    # apply position solve
    TheSystem.LDE.GetSurfaceAt(5).ThicknessCell.SetSolveData(PositionSolve)
    # ! [e07s03_py]
  
    # ! [e07s04_py]
    # create pickup solve
    PickupSolve = TheSystem.LDE.GetSurfaceAt(6).ThicknessCell.CreateSolveType(ZOSAPI.Editors.SolveType.SurfacePickup)
    # solve-specific properties are in ISolveSurfacePickup interface, accessed via _S_SurfacePickup
    PickupSolve._S_SurfacePickup.Surface = 5
    PickupSolve._S_SurfacePickup.ScaleFactor = -1
    PickupSolve._S_SurfacePickup.offset = 0
    PickupSolve._S_SurfacePickup.Column = ZOSAPI.Editors.LDE.SurfaceColumn.Thickness
    # apply the pickup solve
    TheSystem.LDE.GetSurfaceAt(6).ThicknessCell.SetSolveData(PickupSolve)
    # ! [e07s04_py]
  
    # ! [e07s05_py]
    # set pickup solves for coordinate break tilt/decenter parameter cells
    # these parameters are columns 12-16 in the Lens Data Editor (parameters 1-5)
    surf3 = TheSystem.LDE.GetSurfaceAt(3)
    surf6 = TheSystem.LDE.GetSurfaceAt(6)
    ParameterPickup = surf6.GetCellAt(12).CreateSolveType(ZOSAPI.Editors.SolveType.SurfacePickup)
    ParameterPickup._S_SurfacePickup.Surface = 3
    ParameterPickup._S_SurfacePickup.ScaleFactor = -1
    ParameterPickup._S_SurfacePickup.MakePickupFromCurrentColumn()
    for i in range(12, 17):
        surf6.GetCellAt(i).SetSolveData(ParameterPickup)
        surf3.GetCellAt(i).DoubleValue = np.random.uniform(-0.1, .01) # assign random tilt/decenter values
    # also, set the 'order' flag for CB#2
    surf6.GetCellAt(17).IntegerValue = 1
    # ! [e07s05_py]
  
    # ! [e07s06_py]
    # check the global rotation matrix of surface 5 (rear of tilted/decentered lens)
    GLCR = ZOSAPI.Editors.MFE.MeritOperandType.GLCR
    # GLCR operand only uses two input parameters: the surface number, and the rotation matrix entry number.
    # But, GetOperandValue() expects the operand type, plus 8 more inputs because some operands use all 8.
    # So, we will have to put 0's for the additional unused inputs in the function call.
    RotationMatrix = np.zeros([3, 3])
    i = 1
    for x in range(0, 3):
        for y in range(0, 3):
            RotationMatrix[x][y] = TheSystem.MFE.GetOperandValue(GLCR, 5, i, 0, 0, 0, 0, 0, 0)
            i = i + 1
    # ! [e07s06_py]
  
    OutFilePath = TheApplication.ZemaxDataDir + r"\Samples\API\Python\Python_07_TiltDecenterAndMFOperand.zos"
    TheSystem.SaveAs(OutFilePath)
  
    print('Created new file: %s' % OutFilePath)
  
    # This will clean up the connection to OpticStudio.
    # Note that it closes down the server instance of OpticStudio, so you for maximum performance do not do
    # this until you need to.
    del zos
    zos = None

"
```

```markdown
帮我写一个zemax公差分析的脚本，具备如下功能：（1）读取文件；（2）采用公差向导的方式进行配置 (TheSystem.TDE.SEQToleranceWizard)；（3）公差配置的内容包括4项（ElementDecenterX, ElementDecenterY, ElementTiltXDegrees, ElementTiltYDegrees）；（4）可以设置公差仿真的起始面；（5）采用公差仿真向导进行参数设置 (TheSystem.Tools.OpenTolerancing)。构建完整的类和main函数，测试数据与@zpy_fftmtf.py 中的一致

当需要编写与zemax相关的代码时，必须先从 @zos_api1.json @zos_api2.json @zos_api3.json @zos_api4.json @zos_api5.json  文件中进行精确的查找，然后再进行开发。

对于zemax系统连接，引入  **@zpy_connection.py**  包，代码格式参考 @zpy_fftmtf.py 

必须基于sequential thinking MCP服务进行回答

将代码写入 @zpy_tol_wizard.py

基于 @pyzemax-code-master.mdc  @pyzemax-code-style.mdc  

```
#Set up Tolerance Wizard and run it
tWiz = TheSystem.TDE.SEQToleranceWizard
#Specify element tolerances
tWiz.ElementDecenterX = 0.1
tWiz.ElementDecenterY = 0.1
tWiz.ElementTiltXDegrees = 0.2
tWiz.ElementTiltYDegrees = 0.2
#Specify tolerances not to be used
tWiz.IsSurfaceSandAIrregularityUsed = False
tWiz.IsIndexUsed = False
tWiz.IsIndexAbbePercentageUsed = False
tWiz.OK()

#Set up Tolerancing analysis and run it
tol = TheSystem.Tools.OpenTolerancing()
#Select Sensitivity mode
tol.SetupMode =  ZOSAPI.Tools.Tolerancing.SetupModes.Sensitivity
#Select Criterion and related settings
tol.Criterion = ZOSAPI.Tools.Tolerancing.Criterions.RMSSpotRadius
tol.CriterionSampling = 3
tol.CriterionComp = ZOSAPI.Tools.Tolerancing.CriterionComps.OptimizeAll_DLS
tol.CriterionCycle = 2
tol.CriterionField = ZOSAPI.Tools.Tolerancing.CriterionFields.UserDefined
#Select number of MC runs and files to save
tol.NumberOfRuns = 20
tol.NumberToSave = 20
#Run the Tolerancing analysis
tol.RunAndWaitForCompletion()
tol.Close()
```
```

```makrdown
帮我写一个基于公差操作数的公差仿真分析脚本，具体流程如下：
（1）添加公差操作数, 参考 @zos_api5.json 中的"ZOSAPI.Editors.TDE.IToleranceDataEditor"，仅包括：TEDX/TEDY/TETX/TETY
（2）对公差操作数的参数进行设置，参考 @zos_api5.json 中的"ZOSAPI.Editors.TDE.ITDERow"，
（3）执行公差仿真,参考如下代码
```

# Set up Tolerancing analysis and run it

tol = TheSystem.Tools.OpenTolerancing()

# Select Sensitivity mode

tol.SetupMode =  ZOSAPI.Tools.Tolerancing.SetupModes.Sensitivity

# Select Criterion and related settings

tol.Criterion = ZOSAPI.Tools.Tolerancing.Criterions.RMSSpotRadius
tol.CriterionSampling = 3
tol.CriterionComp = ZOSAPI.Tools.Tolerancing.CriterionComps.OptimizeAll_DLS
tol.CriterionCycle = 2
tol.CriterionField = ZOSAPI.Tools.Tolerancing.CriterionFields.UserDefined

# Select number of MC runs and files to save

tol.NumberOfRuns = 20
tol.NumberToSave = 20

# Run the Tolerancing analysis

tol.RunAndWaitForCompletion()
tol.Close()

```

```

## APP

```markdown
我要实现镜子在偏心或者倾斜后，对光学系统的分析，具体要求如下：（1）基于@zpy_transform.py为指定索引的镜子设置偏心和倾斜；（2）偏心和倾斜都是按照范围进行设定；（3）你需要在给定的范围内，对于每一个数值进行计算；（4）如果我指定了一个变量范围，那么在单个变量范围内进行扫描分析；（5）如果我指定了多个变量范围，那么你需要组合这里变量进行分析；（6）仅分析以下变量"x_decenter""y_decenter""z_decenter""x_tilt""y_tilt"；（7）为main函数提供命令行参数配置，文件名必选；（8）先分析系统的wavefront PV和RMS，计算逻辑必须按照@zpy_wavefron.py中的逻辑。
```

```markdown
我要实现镜子在偏心或者倾斜后，对光学系统的分析，具体要求如下：（1）基于@zpy_transform.py为指定索引的镜子设置偏心和倾斜；（2）偏心和倾斜都是按照范围进行设定；（3）你需要在给定的范围内，对于每一个数值进行计算；（4）如果我指定了一个变量范围，那么在单个变量范围内进行扫描分析；（5）如果我指定了多个变量范围，那么你需要组合这里变量进行分析；（6）仅分析以下变量"x_decenter""y_decenter""z_decenter""x_tilt""y_tilt"；（7）为main函数提供命令行参数配置，文件名必选；（8）分析系统的全视场像差，计算逻辑必须按照@zpy_full_aberration.py中的逻辑;(9)用户可以指定参数分析像差的类型，如"PrimaryComa"或者数字"7"(表示第7项像差)；（10）对于像差分析，保存生成的每一张图片，然后当整个程序运行之后，将所有保存的图像生成一张gif图片。

(1)到（7）的逻辑可以完全采用@zpy_cb_wavefront.py。

将上述代码直接写入 @zpy_cb_aberration.py 中
```

```makrdown
我要实现镜子在偏心或者倾斜后，对光学系统的分析，具体要求如下：（1）基于@zpy_transform.py为指定索引的镜子设置偏心和倾斜；（2）偏心和倾斜都是按照范围进行设定，在给定的范围内根据设定的数量进行等间隔采样；（3）我会指定多个变量范围，那么你需要组合这里变量进行分析；（4）仅分析以下变量"x_decenter""y_decenter""z_decenter""x_tilt""y_tilt"；（5）分析系统的zernike系数，计算逻辑必须按照 @zpy_zernike_coeff 中的逻辑，对于每一个分析结果进行命名然后保存（命名规则为分析变量+当前分析的数值）；（6）用户可以指定zernike系数分析的视场、波长、采样率；（7）为main函数提供命令行参数配置，文件名必选；

(1)到（4）的逻辑可以完全复用@zpy_cb_wavefront.py中的相关代码，不要进行非必要的修改。

将上述代码直接写入 @zpy_sensitivity_diff.py 中
```

## 文档编写

```
@zpy_transform.py @zpy_transform_multi.py 分析这两个文件，基于 @py-class-des-master.mdc ，将这两个文件的解释更新到@readme.md 中
```

## 模型

`claud 3.7 thinking`

## MCP

`sequential thinking`
